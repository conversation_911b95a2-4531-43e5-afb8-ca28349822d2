#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 291504128 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3548), pid=42412, tid=30196
#
# JRE version: Java(TM) SE Runtime Environment (17.0.12+8) (build 17.0.12+8-LTS-286)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.12+8-LTS-286, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dmaven.multiModuleProjectDirectory=D:\Channel Partner -Djansi.passthrough=true -Dmaven.home=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\plugins\maven\lib\maven3 -Dclassworlds.conf=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\plugins\maven\lib\maven3\bin\m2.conf -Dmaven.ext.class.path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\plugins\maven\lib\maven-event-listener.jar -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\lib\idea_rt.jar=54853 -Dfile.encoding=UTF-8 org.codehaus.classworlds.Launcher -Didea.version=2024.3.4 clean install -Dmaven.test.skip -U

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Thu Jun  5 13:28:57 2025 India Standard Time elapsed time: 12.966167 seconds (0d 0h 0m 12s)

---------------  T H R E A D  ---------------

Current thread (0x000002af3bb61cd0):  VMThread "VM Thread" [stack: 0x000000d3d5700000,0x000000d3d5800000] [id=30196]

Stack: [0x000000d3d5700000,0x000000d3d5800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67a04a]
V  [jvm.dll+0x7da4ed]
V  [jvm.dll+0x7dbe33]
V  [jvm.dll+0x7dc4a3]
V  [jvm.dll+0x24508f]
V  [jvm.dll+0x677089]
V  [jvm.dll+0x66bd32]
V  [jvm.dll+0x301fa6]
V  [jvm.dll+0x309546]
V  [jvm.dll+0x359d2e]
V  [jvm.dll+0x359f5f]
V  [jvm.dll+0x2d9078]
V  [jvm.dll+0x2d74ad]
V  [jvm.dll+0x2d6afc]
V  [jvm.dll+0x31a981]
V  [jvm.dll+0x7e0d9b]
V  [jvm.dll+0x7e1ad4]
V  [jvm.dll+0x7e1fed]
V  [jvm.dll+0x7e23c4]
V  [jvm.dll+0x7e2490]
V  [jvm.dll+0x78abea]
V  [jvm.dll+0x678f35]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]

VM_Operation (0x000000d3d51fb1f8): G1TryInitiateConcMark, mode: safepoint, requested by thread 0x000002af18a19310


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002af05a19330, length=16, elements={
0x000002af18a19310, 0x000002af3bb6d690, 0x000002af3bb6dd70, 0x000002af3bb7d200,
0x000002af3bb7e6f0, 0x000002af7d008e60, 0x000002af7d00d350, 0x000002af7d00f840,
0x000002af7d00fd60, 0x000002af7d018290, 0x000002af7d1256a0, 0x000002af7d126ff0,
0x000002af7d124c80, 0x000002af7d1260c0, 0x000002af7e51e4a0, 0x000002af7e51e9f0
}

Java Threads: ( => current thread )
  0x000002af18a19310 JavaThread "main" [_thread_blocked, id=39884, stack(0x000000d3d5100000,0x000000d3d5200000)]
  0x000002af3bb6d690 JavaThread "Reference Handler" daemon [_thread_blocked, id=29488, stack(0x000000d3d5800000,0x000000d3d5900000)]
  0x000002af3bb6dd70 JavaThread "Finalizer" daemon [_thread_blocked, id=38172, stack(0x000000d3d5900000,0x000000d3d5a00000)]
  0x000002af3bb7d200 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=28252, stack(0x000000d3d5a00000,0x000000d3d5b00000)]
  0x000002af3bb7e6f0 JavaThread "Attach Listener" daemon [_thread_blocked, id=17232, stack(0x000000d3d5b00000,0x000000d3d5c00000)]
  0x000002af7d008e60 JavaThread "Service Thread" daemon [_thread_blocked, id=43664, stack(0x000000d3d5c00000,0x000000d3d5d00000)]
  0x000002af7d00d350 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=26168, stack(0x000000d3d5d00000,0x000000d3d5e00000)]
  0x000002af7d00f840 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=27400, stack(0x000000d3d5e00000,0x000000d3d5f00000)]
  0x000002af7d00fd60 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=39732, stack(0x000000d3d5f00000,0x000000d3d6000000)]
  0x000002af7d018290 JavaThread "Sweeper thread" daemon [_thread_blocked, id=28412, stack(0x000000d3d6000000,0x000000d3d6100000)]
  0x000002af7d1256a0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=26884, stack(0x000000d3d6100000,0x000000d3d6200000)]
  0x000002af7d126ff0 JavaThread "Monitor Ctrl-Break" daemon [_thread_in_native, id=6156, stack(0x000000d3d6200000,0x000000d3d6300000)]
  0x000002af7d124c80 JavaThread "Notification Thread" daemon [_thread_blocked, id=43464, stack(0x000000d3d6300000,0x000000d3d6400000)]
  0x000002af7d1260c0 JavaThread "Thread-1" daemon [_thread_blocked, id=36284, stack(0x000000d3d6f00000,0x000000d3d7000000)]
  0x000002af7e51e4a0 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=8736, stack(0x000000d3d6500000,0x000000d3d6600000)]
  0x000002af7e51e9f0 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=30248, stack(0x000000d3d6600000,0x000000d3d6700000)]

Other Threads:
=>0x000002af3bb61cd0 VMThread "VM Thread" [stack: 0x000000d3d5700000,0x000000d3d5800000] [id=30196]
  0x000002af7d27abf0 WatcherThread [stack: 0x000000d3d6400000,0x000000d3d6500000] [id=29768]
  0x000002af166de7b0 GCTaskThread "GC Thread#0" [stack: 0x000000d3d5200000,0x000000d3d5300000] [id=43836]
  0x000002af7d1ac530 GCTaskThread "GC Thread#1" [stack: 0x000000d3d6700000,0x000000d3d6800000] [id=30604]
  0x000002af7d1aac70 GCTaskThread "GC Thread#2" [stack: 0x000000d3d6800000,0x000000d3d6900000] [id=42292]
  0x000002af7d1aaf30 GCTaskThread "GC Thread#3" [stack: 0x000000d3d6900000,0x000000d3d6a00000] [id=12240]
  0x000002af7d1ac7f0 GCTaskThread "GC Thread#4" [stack: 0x000000d3d6a00000,0x000000d3d6b00000] [id=41892]
  0x000002af7d1ab1f0 GCTaskThread "GC Thread#5" [stack: 0x000000d3d6b00000,0x000000d3d6c00000] [id=36044]
  0x000002af7d1ab4b0 GCTaskThread "GC Thread#6" [stack: 0x000000d3d6c00000,0x000000d3d6d00000] [id=2956]
  0x000002af7d1ab770 GCTaskThread "GC Thread#7" [stack: 0x000000d3d6d00000,0x000000d3d6e00000] [id=25300]
  0x000002af7d1aba30 GCTaskThread "GC Thread#8" [stack: 0x000000d3d6e00000,0x000000d3d6f00000] [id=38344]
  0x000002af7f8a53a0 GCTaskThread "GC Thread#9" [stack: 0x000000d3d7000000,0x000000d3d7100000] [id=34640]
  0x000002af18a917f0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000d3d5300000,0x000000d3d5400000] [id=31776]
  0x000002af18a952e0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000d3d5400000,0x000000d3d5500000] [id=22920]
  0x000002af7f8a5be0 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000d3d7200000,0x000000d3d7300000] [id=33104]
  0x000002af7f8a6c60 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000d3d7300000,0x000000d3d7400000] [id=39164]
  0x000002af3ba1e140 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000d3d5500000,0x000000d3d5600000] [id=37848]
  0x000002af0519c920 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000d3d7400000,0x000000d3d7500000] [id=21128]
  0x000002af3ba1e430 ConcurrentGCThread "G1 Service" [stack: 0x000000d3d5600000,0x000000d3d5700000] [id=36572]

Threads with active compile tasks:
C2 CompilerThread0    12986 8592   !   4       com.sun.tools.javac.code.ClassFinder::fillIn (322 bytes)
C2 CompilerThread1    12986 8602       4       com.sun.tools.javac.comp.Attr::checkIdInternal (788 bytes)
C2 CompilerThread2    12986 8635       4       com.sun.tools.javac.comp.Resolve::findGlobalType (111 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000002af18a15b80] Threads_lock - owner thread: 0x000002af3bb61cd0
[0x000002af18a15280] Heap_lock - owner thread: 0x000002af18a19310

Heap address: 0x0000000702400000, size: 4060 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000002af3c000000-0x000002af3cbd0000-0x000002af3cbd0000), size 12386304, SharedBaseAddress: 0x000002af3c000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002af3d000000-0x000002af7d000000, reserved size: 1073741824
Narrow klass base: 0x000002af3c000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 16235M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4060M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 262144K, used 128975K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 35512K, committed 35840K, reserved 1114112K
  class space    used 3995K, committed 4160K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702600000, 0x0000000702600000| Untracked 
|   1|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%| O|  |TAMS 0x0000000702800000, 0x0000000702800000| Untracked 
|   2|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%| O|  |TAMS 0x0000000702a00000, 0x0000000702a00000| Untracked 
|   3|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702c00000, 0x0000000702c00000| Untracked 
|   4|0x0000000702c00000, 0x0000000702e00000, 0x0000000702e00000|100%| O|  |TAMS 0x0000000702e00000, 0x0000000702e00000| Untracked 
|   5|0x0000000702e00000, 0x0000000703000000, 0x0000000703000000|100%| O|  |TAMS 0x0000000703000000, 0x0000000703000000| Untracked 
|   6|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%| O|  |TAMS 0x0000000703200000, 0x0000000703200000| Untracked 
|   7|0x0000000703200000, 0x00000007033d5400, 0x0000000703400000| 91%| O|  |TAMS 0x00000007033d5400, 0x00000007033d5400| Untracked 
|   8|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703600000, 0x0000000703600000| Untracked 
|   9|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703800000, 0x0000000703800000| Untracked 
|  10|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%| O|  |TAMS 0x0000000703a00000, 0x0000000703a00000| Untracked 
|  11|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%|HS|  |TAMS 0x0000000703a00000, 0x0000000703c00000| Complete 
|  12|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703e00000, 0x0000000703e00000| Untracked 
|  13|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%| O|  |TAMS 0x0000000704000000, 0x0000000704000000| Untracked 
|  14|0x0000000704000000, 0x0000000704200000, 0x0000000704200000|100%| O|  |TAMS 0x0000000704200000, 0x0000000704200000| Untracked 
|  15|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704400000, 0x0000000704400000| Untracked 
|  16|0x0000000704400000, 0x0000000704600000, 0x0000000704600000|100%| O|  |TAMS 0x0000000704600000, 0x0000000704600000| Untracked 
|  17|0x0000000704600000, 0x0000000704800000, 0x0000000704800000|100%| O|  |TAMS 0x0000000704800000, 0x0000000704800000| Untracked 
|  18|0x0000000704800000, 0x0000000704a00000, 0x0000000704a00000|100%| O|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|  19|0x0000000704a00000, 0x0000000704c00000, 0x0000000704c00000|100%| O|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|  20|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  21|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000705000000, 0x0000000705000000| Untracked 
|  22|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|  23|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705400000, 0x0000000705400000| Untracked 
|  24|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|  25|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705800000, 0x0000000705800000| Untracked 
|  26|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Untracked 
|  27|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705c00000, 0x0000000705c00000| Untracked 
|  28|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705e00000, 0x0000000705e00000| Untracked 
|  29|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000706000000, 0x0000000706000000| Untracked 
|  30|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706200000, 0x0000000706200000| Untracked 
|  31|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706400000, 0x0000000706400000| Untracked 
|  32|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706600000, 0x0000000706600000| Untracked 
|  33|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706800000, 0x0000000706800000| Untracked 
|  34|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706a00000, 0x0000000706a00000| Untracked 
|  35|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706c00000, 0x0000000706c00000| Untracked 
|  36|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|  |TAMS 0x0000000706e00000, 0x0000000706e00000| Untracked 
|  37|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| O|  |TAMS 0x0000000707000000, 0x0000000707000000| Untracked 
|  38|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707200000, 0x0000000707200000| Untracked 
|  39|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707400000, 0x0000000707400000| Untracked 
|  40|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707600000, 0x0000000707600000| Untracked 
|  41|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707800000, 0x0000000707800000| Untracked 
|  42|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| O|  |TAMS 0x0000000707a00000, 0x0000000707a00000| Untracked 
|  43|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| O|  |TAMS 0x0000000707c00000, 0x0000000707c00000| Untracked 
|  44|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| O|  |TAMS 0x0000000707e00000, 0x0000000707e00000| Untracked 
|  45|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| O|  |TAMS 0x0000000708000000, 0x0000000708000000| Untracked 
|  46|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| O|  |TAMS 0x0000000708200000, 0x0000000708200000| Untracked 
|  47|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| O|  |TAMS 0x0000000708400000, 0x0000000708400000| Untracked 
|  48|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| O|  |TAMS 0x0000000708600000, 0x0000000708600000| Untracked 
|  49|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| O|  |TAMS 0x0000000708800000, 0x0000000708800000| Untracked 
|  50|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| O|  |TAMS 0x0000000708a00000, 0x0000000708a00000| Untracked 
|  51|0x0000000708a00000, 0x0000000708c00000, 0x0000000708c00000|100%| O|  |TAMS 0x0000000708c00000, 0x0000000708c00000| Untracked 
|  52|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%| O|  |TAMS 0x0000000708e00000, 0x0000000708e00000| Untracked 
|  53|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%| O|  |TAMS 0x0000000709000000, 0x0000000709000000| Untracked 
|  54|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000, 0x0000000709000000| Untracked 
|  55|0x0000000709200000, 0x0000000709400000, 0x0000000709400000|100%| O|  |TAMS 0x0000000709200000, 0x0000000709400000| Untracked 
|  56|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| O|  |TAMS 0x0000000709400000, 0x0000000709600000| Untracked 
|  57|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| O|  |TAMS 0x0000000709600000, 0x0000000709800000| Untracked 
|  58|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| O|  |TAMS 0x0000000709800000, 0x0000000709a00000| Untracked 
|  59|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| O|  |TAMS 0x0000000709a00000, 0x0000000709c00000| Untracked 
|  60|0x0000000709c00000, 0x0000000709e00000, 0x0000000709e00000|100%| O|  |TAMS 0x0000000709c00000, 0x0000000709e00000| Untracked 
|  61|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%| O|  |TAMS 0x0000000709e00000, 0x000000070a000000| Untracked 
|  62|0x000000070a000000, 0x000000070a0c6200, 0x000000070a200000| 38%| O|  |TAMS 0x000000070a000000, 0x000000070a0c6200| Untracked 
|  63|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000, 0x000000070a200000| Untracked 
|  64|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000, 0x000000070a400000| Untracked 
|  65|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000, 0x000000070a600000| Untracked 
|  66|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000, 0x000000070a800000| Untracked 
|  67|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000, 0x000000070aa00000| Untracked 
|  68|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000, 0x000000070ac00000| Untracked 
|  69|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000, 0x000000070ae00000| Untracked 
|  70|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000, 0x000000070b000000| Untracked 
|  71|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000, 0x000000070b200000| Untracked 
|  72|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000, 0x000000070b400000| Untracked 
|  73|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000, 0x000000070b600000| Untracked 
|  74|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000, 0x000000070b800000| Untracked 
|  75|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000, 0x000000070ba00000| Untracked 
|  76|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000, 0x000000070bc00000| Untracked 
|  77|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000, 0x000000070be00000| Untracked 
|  78|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  79|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  80|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  81|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
|  82|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
|  83|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000, 0x000000070ca00000| Untracked 
|  84|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000, 0x000000070cc00000| Untracked 
|  85|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000, 0x000000070ce00000| Untracked 
|  86|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000, 0x000000070d000000| Untracked 
|  87|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000, 0x000000070d200000| Untracked 
|  88|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000, 0x000000070d400000| Untracked 
|  89|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000, 0x000000070d600000| Untracked 
|  90|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000, 0x000000070d800000| Untracked 
|  91|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000, 0x000000070da00000| Untracked 
|  92|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000, 0x000000070dc00000| Untracked 
|  93|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000, 0x000000070de00000| Untracked 
|  94|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000, 0x000000070e000000| Untracked 
|  95|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000, 0x000000070e200000| Untracked 
|  96|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000, 0x000000070e400000| Untracked 
|  97|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000, 0x000000070e600000| Untracked 
|  98|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000, 0x000000070e800000| Untracked 
|  99|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000, 0x000000070ea00000| Untracked 
| 100|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000, 0x000000070ec00000| Untracked 
| 101|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000, 0x000000070ee00000| Untracked 
| 102|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000, 0x000000070f000000| Untracked 
| 103|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000, 0x000000070f200000| Untracked 
| 104|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000, 0x000000070f400000| Untracked 
| 105|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000, 0x000000070f600000| Untracked 
| 106|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000, 0x000000070f800000| Untracked 
| 107|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000, 0x000000070fa00000| Untracked 
| 108|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Untracked 
| 109|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000, 0x000000070fe00000| Untracked 
| 110|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000, 0x0000000710000000| Untracked 
| 111|0x0000000710200000, 0x0000000710358880, 0x0000000710400000| 67%| S|CS|TAMS 0x0000000710200000, 0x0000000710200000| Complete 
| 112|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000, 0x0000000710400000| Untracked 
| 113|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000, 0x0000000710600000| Untracked 
| 114|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000, 0x0000000710800000| Untracked 
| 115|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000, 0x0000000710a00000| Untracked 
| 116|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000, 0x0000000710c00000| Untracked 
| 117|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000, 0x0000000710e00000| Untracked 
| 118|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000, 0x0000000711000000| Untracked 
| 119|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000, 0x0000000711200000| Untracked 
| 120|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000, 0x0000000711400000| Untracked 
| 121|0x0000000711600000, 0x0000000711600000, 0x0000000711800000|  0%| F|  |TAMS 0x0000000711600000, 0x0000000711600000| Untracked 
| 122|0x0000000711800000, 0x0000000711800000, 0x0000000711a00000|  0%| F|  |TAMS 0x0000000711800000, 0x0000000711800000| Untracked 
| 123|0x0000000711a00000, 0x0000000711a00000, 0x0000000711c00000|  0%| F|  |TAMS 0x0000000711a00000, 0x0000000711a00000| Untracked 
| 124|0x0000000711c00000, 0x0000000711c00000, 0x0000000711e00000|  0%| F|  |TAMS 0x0000000711c00000, 0x0000000711c00000| Untracked 
| 125|0x0000000711e00000, 0x0000000711e00000, 0x0000000712000000|  0%| F|  |TAMS 0x0000000711e00000, 0x0000000711e00000| Untracked 
| 126|0x0000000712000000, 0x0000000712000000, 0x0000000712200000|  0%| F|  |TAMS 0x0000000712000000, 0x0000000712000000| Untracked 
|2029|0x00000007ffe00000, 0x0000000800000000, 0x0000000800000000|100%| O|  |TAMS 0x0000000800000000, 0x0000000800000000| Untracked 

Card table byte_map: [0x000002af30010000,0x000002af30800000] _byte_map_base: 0x000002af2c7fe000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002af18a81220, (CMBitMap*) 0x000002af18a811e0
 Prev Bits: [0x000002af34f60000, 0x000002af38ed0000)
 Next Bits: [0x000002af30ff0000, 0x000002af34f60000)

Polling page: 0x000002af180e0000

Metaspace:

Usage:
  Non-class:     30.78 MB used.
      Class:      3.90 MB used.
       Both:     34.68 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      30.94 MB ( 48%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.06 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      35.00 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  928.00 KB
       Class:  11.98 MB
        Both:  12.89 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.12 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 210.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 560.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 3.
num_chunks_taken_from_freelist: 1328.
num_chunk_merges: 0.
num_chunk_splits: 917.
num_chunks_enlarged: 707.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=5292Kb max_used=5292Kb free=114707Kb
 bounds [0x000002af27da0000, 0x000002af282d0000, 0x000002af2f2d0000]
CodeHeap 'profiled nmethods': size=120000Kb used=16219Kb max_used=16219Kb free=103780Kb
 bounds [0x000002af202d0000, 0x000002af212b0000, 0x000002af27800000]
CodeHeap 'non-nmethods': size=5760Kb used=1289Kb max_used=1343Kb free=4470Kb
 bounds [0x000002af27800000, 0x000002af27a70000, 0x000002af27da0000]
 total_blobs=7907 nmethods=7338 adapters=479
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 10.910 Thread 0x000002af7d00fd60 8632       3       com.sun.tools.javac.code.Lint::<init> (35 bytes)
Event: 10.910 Thread 0x000002af7d00fd60 nmethod 8632 0x000002af212a0b10 code [0x000002af212a0ce0, 0x000002af212a1138]
Event: 10.910 Thread 0x000002af7d00fd60 8631       3       com.sun.tools.javac.comp.Check::checkUniqueImport (130 bytes)
Event: 10.910 Thread 0x000002af7d00fd60 nmethod 8631 0x000002af212a1290 code [0x000002af212a1540, 0x000002af212a2358]
Event: 10.911 Thread 0x000002af7e51e4a0 nmethod 8599 0x000002af282bf910 code [0x000002af282bfa80, 0x000002af282bfd58]
Event: 10.911 Thread 0x000002af7e51e4a0 8601       4       com.sun.tools.javac.comp.Attr::checkId (59 bytes)
Event: 10.912 Thread 0x000002af7d00fd60 8633   !   3       com.sun.tools.javac.comp.TypeEnter$Phase::doCompleteEnvs (247 bytes)
Event: 10.912 Thread 0x000002af7e51e4a0 nmethod 8601 0x000002af282bfe10 code [0x000002af282bffa0, 0x000002af282c00a8]
Event: 10.912 Thread 0x000002af7e51e4a0 8634       4       com.sun.tools.javac.code.Types$UnaryVisitor::visit (7 bytes)
Event: 10.913 Thread 0x000002af7d00fd60 nmethod 8633 0x000002af212a2810 code [0x000002af212a2b80, 0x000002af212a4bc8]
Event: 10.913 Thread 0x000002af7e51e4a0 nmethod 8634 0x000002af282c0210 code [0x000002af282c03a0, 0x000002af282c0478]
Event: 10.913 Thread 0x000002af7e51e4a0 8602       4       com.sun.tools.javac.comp.Attr::checkIdInternal (788 bytes)
Event: 10.916 Thread 0x000002af7d00fd60 8640       3       com.sun.tools.javac.comp.Attr::visitTypeIdent (32 bytes)
Event: 10.916 Thread 0x000002af7d00fd60 nmethod 8640 0x000002af212a5790 code [0x000002af212a5940, 0x000002af212a5b88]
Event: 10.919 Thread 0x000002af7d00fd60 8641       2       com.sun.tools.javac.code.ClassFinder::fillIn (136 bytes)
Event: 10.920 Thread 0x000002af7d00fd60 nmethod 8641 0x000002af212a5d10 code [0x000002af212a5fa0, 0x000002af212a67f8]
Event: 10.921 Thread 0x000002af7d00fd60 8642       2       java.io.DataInputStream::readLong (128 bytes)
Event: 10.921 Thread 0x000002af7d00fd60 nmethod 8642 0x000002af212a6c10 code [0x000002af212a6dc0, 0x000002af212a70c8]
Event: 10.928 Thread 0x000002af7e51e9f0 nmethod 8301 0x000002af282c0590 code [0x000002af282c0b00, 0x000002af282c77c0]
Event: 10.930 Thread 0x000002af7e51e9f0 8635       4       com.sun.tools.javac.comp.Resolve::findGlobalType (111 bytes)

GC Heap History (20 events):
Event: 9.033 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 110592K, used 62829K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 33808K, committed 34176K, reserved 1114112K
  class space    used 3860K, committed 4032K, reserved 1048576K
}
Event: 9.162 GC heap before
{Heap before GC invocations=17 (full 0):
 garbage-first heap   total 112640K, used 81261K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 2 survivors (4096K)
 Metaspace       used 33813K, committed 34176K, reserved 1114112K
  class space    used 3860K, committed 4032K, reserved 1048576K
}
Event: 9.172 GC heap after
{Heap after GC invocations=18 (full 0):
 garbage-first heap   total 112640K, used 67206K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 33813K, committed 34176K, reserved 1114112K
  class space    used 3860K, committed 4032K, reserved 1048576K
}
Event: 9.234 GC heap before
{Heap before GC invocations=18 (full 0):
 garbage-first heap   total 112640K, used 79494K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 2 survivors (4096K)
 Metaspace       used 33828K, committed 34176K, reserved 1114112K
  class space    used 3860K, committed 4032K, reserved 1048576K
}
Event: 9.242 GC heap after
{Heap after GC invocations=19 (full 0):
 garbage-first heap   total 112640K, used 71890K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 33828K, committed 34176K, reserved 1114112K
  class space    used 3860K, committed 4032K, reserved 1048576K
}
Event: 9.349 GC heap before
{Heap before GC invocations=20 (full 0):
 garbage-first heap   total 133120K, used 86226K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 9 young (18432K), 2 survivors (4096K)
 Metaspace       used 33980K, committed 34304K, reserved 1114112K
  class space    used 3881K, committed 4032K, reserved 1048576K
}
Event: 9.356 GC heap after
{Heap after GC invocations=21 (full 0):
 garbage-first heap   total 133120K, used 75399K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 33980K, committed 34304K, reserved 1114112K
  class space    used 3881K, committed 4032K, reserved 1048576K
}
Event: 9.593 GC heap before
{Heap before GC invocations=21 (full 0):
 garbage-first heap   total 133120K, used 97927K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 13 young (26624K), 2 survivors (4096K)
 Metaspace       used 34402K, committed 34752K, reserved 1114112K
  class space    used 3924K, committed 4096K, reserved 1048576K
}
Event: 9.600 GC heap after
{Heap after GC invocations=22 (full 0):
 garbage-first heap   total 243712K, used 82847K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 34402K, committed 34752K, reserved 1114112K
  class space    used 3924K, committed 4096K, reserved 1048576K
}
Event: 10.196 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 243712K, used 156575K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 38 young (77824K), 2 survivors (4096K)
 Metaspace       used 34563K, committed 34880K, reserved 1114112K
  class space    used 3941K, committed 4096K, reserved 1048576K
}
Event: 10.207 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 243712K, used 111911K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 34563K, committed 34880K, reserved 1114112K
  class space    used 3941K, committed 4096K, reserved 1048576K
}
Event: 10.236 GC heap before
{Heap before GC invocations=24 (full 0):
 garbage-first heap   total 243712K, used 113959K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 5 survivors (10240K)
 Metaspace       used 34642K, committed 35008K, reserved 1114112K
  class space    used 3949K, committed 4096K, reserved 1048576K
}
Event: 10.242 GC heap after
{Heap after GC invocations=25 (full 0):
 garbage-first heap   total 243712K, used 113704K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 34642K, committed 35008K, reserved 1114112K
  class space    used 3949K, committed 4096K, reserved 1048576K
}
Event: 10.510 GC heap before
{Heap before GC invocations=25 (full 0):
 garbage-first heap   total 243712K, used 144424K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 1 survivors (2048K)
 Metaspace       used 35334K, committed 35648K, reserved 1114112K
  class space    used 3990K, committed 4160K, reserved 1048576K
}
Event: 10.517 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total 243712K, used 119031K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 35334K, committed 35648K, reserved 1114112K
  class space    used 3990K, committed 4160K, reserved 1048576K
}
Event: 10.864 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 243712K, used 176375K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 31 young (63488K), 4 survivors (8192K)
 Metaspace       used 35502K, committed 35840K, reserved 1114112K
  class space    used 3995K, committed 4160K, reserved 1048576K
}
Event: 10.879 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 243712K, used 128418K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 35502K, committed 35840K, reserved 1114112K
  class space    used 3995K, committed 4160K, reserved 1048576K
}
Event: 10.889 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 243712K, used 130466K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 4 survivors (8192K)
 Metaspace       used 35502K, committed 35840K, reserved 1114112K
  class space    used 3995K, committed 4160K, reserved 1048576K
}
Event: 10.897 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 243712K, used 128947K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 35502K, committed 35840K, reserved 1114112K
  class space    used 3995K, committed 4160K, reserved 1048576K
}
Event: 10.936 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 243712K, used 137139K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 1 survivors (2048K)
 Metaspace       used 35512K, committed 35840K, reserved 1114112K
  class space    used 3995K, committed 4160K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 10.803 Thread 0x000002af18a19310 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002af2829e1d0 relative=0x0000000000000490
Event: 10.803 Thread 0x000002af18a19310 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002af2829e1d0 method=com.sun.tools.javac.code.Scope$ScopeImpl.lookup(Lcom/sun/tools/javac/util/Name;Ljava/util/function/Predicate;)Lcom/sun/tools/javac/code/Scope$Entry; @ 53 c2
Event: 10.803 Thread 0x000002af18a19310 DEOPT PACKING pc=0x000002af2829e1d0 sp=0x000000d3d51fd1a0
Event: 10.803 Thread 0x000002af18a19310 DEOPT UNPACKING pc=0x000002af27855ba3 sp=0x000000d3d51fd000 mode 2
Event: 10.839 Thread 0x000002af18a19310 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002af2829e1d0 relative=0x0000000000000490
Event: 10.839 Thread 0x000002af18a19310 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002af2829e1d0 method=com.sun.tools.javac.code.Scope$ScopeImpl.lookup(Lcom/sun/tools/javac/util/Name;Ljava/util/function/Predicate;)Lcom/sun/tools/javac/code/Scope$Entry; @ 53 c2
Event: 10.839 Thread 0x000002af18a19310 DEOPT PACKING pc=0x000002af2829e1d0 sp=0x000000d3d51fd1a0
Event: 10.840 Thread 0x000002af18a19310 DEOPT UNPACKING pc=0x000002af27855ba3 sp=0x000000d3d51fd000 mode 2
Event: 10.840 Thread 0x000002af18a19310 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002af2829e1d0 relative=0x0000000000000490
Event: 10.840 Thread 0x000002af18a19310 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002af2829e1d0 method=com.sun.tools.javac.code.Scope$ScopeImpl.lookup(Lcom/sun/tools/javac/util/Name;Ljava/util/function/Predicate;)Lcom/sun/tools/javac/code/Scope$Entry; @ 53 c2
Event: 10.840 Thread 0x000002af18a19310 DEOPT PACKING pc=0x000002af2829e1d0 sp=0x000000d3d51fd1a0
Event: 10.840 Thread 0x000002af18a19310 DEOPT UNPACKING pc=0x000002af27855ba3 sp=0x000000d3d51fd000 mode 2
Event: 10.840 Thread 0x000002af18a19310 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002af282a0344 relative=0x0000000000000484
Event: 10.840 Thread 0x000002af18a19310 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002af282a0344 method=com.sun.tools.javac.code.Scope$ScopeImpl.lookup(Lcom/sun/tools/javac/util/Name;Ljava/util/function/Predicate;)Lcom/sun/tools/javac/code/Scope$Entry; @ 53 c2
Event: 10.840 Thread 0x000002af18a19310 DEOPT PACKING pc=0x000002af282a0344 sp=0x000000d3d51fd100
Event: 10.840 Thread 0x000002af18a19310 DEOPT UNPACKING pc=0x000002af27855ba3 sp=0x000000d3d51fcff0 mode 2
Event: 10.858 Thread 0x000002af18a19310 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002af2829534c relative=0x0000000000004ccc
Event: 10.858 Thread 0x000002af18a19310 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002af2829534c method=com.sun.tools.javac.jvm.ClassReader.classSigToType()Lcom/sun/tools/javac/code/Type; @ 254 c2
Event: 10.858 Thread 0x000002af18a19310 DEOPT PACKING pc=0x000002af2829534c sp=0x000000d3d51fae70
Event: 10.858 Thread 0x000002af18a19310 DEOPT UNPACKING pc=0x000002af27855ba3 sp=0x000000d3d51fae88 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 10.934 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x00000007104442f0}> (0x00000007104442f0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.934 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x0000000710444588}> (0x0000000710444588) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.934 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x00000007104481b8}> (0x00000007104481b8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.934 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x00000007104483f8}> (0x00000007104483f8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.934 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x000000071044c310}> (0x000000071044c310) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.934 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x000000071044c5a0}> (0x000000071044c5a0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.934 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x000000071044d9e8}> (0x000000071044d9e8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.934 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x000000071044dd08}> (0x000000071044dd08) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.935 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x0000000710463410}> (0x0000000710463410) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.935 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x00000007104636e0}> (0x00000007104636e0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.935 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x0000000710468c50}> (0x0000000710468c50) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.935 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x0000000710468ee0}> (0x0000000710468ee0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.935 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x0000000710477710}> (0x0000000710477710) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.935 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x00000007104779e0}> (0x00000007104779e0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.935 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x0000000710478e58}> (0x0000000710478e58) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.935 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x00000007104791b8}> (0x00000007104791b8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.936 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x000000071048efa0}> (0x000000071048efa0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.936 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x000000071048f2c0}> (0x000000071048f2c0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.936 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x00000007104a0ec8}> (0x00000007104a0ec8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 10.936 Thread 0x000002af18a19310 Exception <a 'sun/nio/fs/WindowsException'{0x00000007104a1198}> (0x00000007104a1198) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]

VM Operations (20 events):
Event: 10.334 Executing VM operation: HandshakeAllThreads done
Event: 10.444 Executing VM operation: ICBufferFull
Event: 10.444 Executing VM operation: ICBufferFull done
Event: 10.510 Executing VM operation: G1TryInitiateConcMark
Event: 10.517 Executing VM operation: G1TryInitiateConcMark done
Event: 10.559 Executing VM operation: G1Concurrent
Event: 10.565 Executing VM operation: G1Concurrent done
Event: 10.582 Executing VM operation: G1Concurrent
Event: 10.583 Executing VM operation: G1Concurrent done
Event: 10.725 Executing VM operation: HandshakeAllThreads
Event: 10.725 Executing VM operation: HandshakeAllThreads done
Event: 10.765 Executing VM operation: HandshakeAllThreads
Event: 10.765 Executing VM operation: HandshakeAllThreads done
Event: 10.768 Executing VM operation: ICBufferFull
Event: 10.768 Executing VM operation: ICBufferFull done
Event: 10.864 Executing VM operation: G1CollectForAllocation
Event: 10.879 Executing VM operation: G1CollectForAllocation done
Event: 10.889 Executing VM operation: G1CollectForAllocation
Event: 10.897 Executing VM operation: G1CollectForAllocation done
Event: 10.936 Executing VM operation: G1TryInitiateConcMark

Events (20 events):
Event: 10.224 loading class java/nio/file/FileTreeWalker$DirectoryNode
Event: 10.224 loading class java/nio/file/FileTreeWalker$DirectoryNode done
Event: 10.224 loading class java/nio/file/FileTreeWalker$Event
Event: 10.224 loading class java/nio/file/FileTreeWalker$Event done
Event: 10.224 loading class java/nio/file/FileTreeWalker$EventType
Event: 10.224 loading class java/nio/file/FileTreeWalker$EventType done
Event: 10.224 loading class java/nio/file/Files$3
Event: 10.224 loading class java/nio/file/Files$3 done
Event: 10.224 loading class java/nio/file/FileVisitResult
Event: 10.224 loading class java/nio/file/FileVisitResult done
Event: 10.334 loading class jdk/internal/jimage/ImageReader$LinkNode
Event: 10.334 loading class jdk/internal/jimage/ImageReader$LinkNode done
Event: 10.736 Thread 0x000002af7d018290 flushing nmethod 0x000002af20eb1a10
Event: 10.736 Thread 0x000002af7d018290 flushing nmethod 0x000002af20eb2790
Event: 10.737 Thread 0x000002af7d018290 flushing nmethod 0x000002af20fbdf90
Event: 10.737 Thread 0x000002af7d018290 flushing nmethod 0x000002af20fc2090
Event: 10.737 Thread 0x000002af7d018290 flushing nmethod 0x000002af20fc5e10
Event: 10.737 Thread 0x000002af7d018290 flushing nmethod 0x000002af20fc6410
Event: 10.737 Thread 0x000002af7d018290 flushing nmethod 0x000002af20fed310
Event: 10.737 Thread 0x000002af7d018290 flushing nmethod 0x000002af21013710


Dynamic libraries:
0x00007ff7980a0000 - 0x00007ff7980b0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ffbcf000000 - 0x00007ffbcf266000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffbce5f0000 - 0x00007ffbce6b9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffbcc3a0000 - 0x00007ffbcc76c000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffbcc190000 - 0x00007ffbcc2db000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb8a6a0000 - 0x00007ffb8a6bb000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ffb8a6c0000 - 0x00007ffb8a6d9000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ffbcdab0000 - 0x00007ffbcdb62000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffbcda00000 - 0x00007ffbcdaa9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffbcebd0000 - 0x00007ffbcec76000 	C:\WINDOWS\System32\sechost.dll
0x00007ffbcecb0000 - 0x00007ffbcedc6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffbcdb70000 - 0x00007ffbcdd3a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffbcca30000 - 0x00007ffbcca57000 	C:\WINDOWS\System32\win32u.dll
0x00007ffbb0530000 - 0x00007ffbb07ca000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffbcec80000 - 0x00007ffbcecab000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffbcc770000 - 0x00007ffbcc8a2000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffbcca60000 - 0x00007ffbccb03000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffbc4e30000 - 0x00007ffbc4e3b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffbcd6d0000 - 0x00007ffbcd700000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb8a690000 - 0x00007ffb8a69c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ffb8a600000 - 0x00007ffb8a68e000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ffb45160000 - 0x00007ffb45d40000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ffbce6c0000 - 0x00007ffbce6c8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb81a60000 - 0x00007ffb81a6a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffbbcc10000 - 0x00007ffbbcc46000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffbcea10000 - 0x00007ffbcea84000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffbcaf30000 - 0x00007ffbcaf4a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb8a5f0000 - 0x00007ffb8a5fa000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ffbc9850000 - 0x00007ffbc9a91000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffbce260000 - 0x00007ffbce5e4000 	C:\WINDOWS\System32\combase.dll
0x00007ffbcd550000 - 0x00007ffbcd630000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffbb31d0000 - 0x00007ffbb3209000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffbccd20000 - 0x00007ffbccdb9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffbc5690000 - 0x00007ffbc569e000 	C:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ffb8a5c0000 - 0x00007ffb8a5e5000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ffb46400000 - 0x00007ffb464d7000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ffbcce20000 - 0x00007ffbcd54d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffbcc8b0000 - 0x00007ffbcca24000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffbc9de0000 - 0x00007ffbca636000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffbce8e0000 - 0x00007ffbce9cf000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffbcdd40000 - 0x00007ffbcdda9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffbcc0a0000 - 0x00007ffbcc0cf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb8a560000 - 0x00007ffb8a578000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007ffb8a5a0000 - 0x00007ffb8a5b9000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007ffbc3cd0000 - 0x00007ffbc3dee000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffbcb4c0000 - 0x00007ffbcb52a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb8a580000 - 0x00007ffb8a596000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007ffbbf010000 - 0x00007ffbbf034000 	C:\Users\<USER>\AppData\Local\Temp\jansi-2.4.1-e4db5489ff0371c8-jansi.dll
0x00007ffb8a4d0000 - 0x00007ffb8a4e0000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007ffbcb770000 - 0x00007ffbcb78c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffbcae90000 - 0x00007ffbcaeca000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffbcb560000 - 0x00007ffbcb58b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffbcc070000 - 0x00007ffbcc096000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffbcb760000 - 0x00007ffbcb76c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffbca940000 - 0x00007ffbca973000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffbce8d0000 - 0x00007ffbce8da000 	C:\WINDOWS\System32\NSI.dll
0x00007ffbc4000000 - 0x00007ffbc401f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffbc3fd0000 - 0x00007ffbc3ff5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffbca9d0000 - 0x00007ffbcaaf7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffbb7200000 - 0x00007ffbb720b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffbc2dc0000 - 0x00007ffbc2e46000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffb46de0000 - 0x00007ffb46dee000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007ffbccba0000 - 0x00007ffbccd17000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffbcb9e0000 - 0x00007ffbcba10000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffbcb990000 - 0x00007ffbcb9cf000 	C:\WINDOWS\SYSTEM32\NTASN1.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\AppData\Local\Temp

VM Arguments:
jvm_args: -Dmaven.multiModuleProjectDirectory=D:\Channel Partner -Djansi.passthrough=true -Dmaven.home=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\plugins\maven\lib\maven3 -Dclassworlds.conf=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\plugins\maven\lib\maven3\bin\m2.conf -Dmaven.ext.class.path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\plugins\maven\lib\maven-event-listener.jar -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\lib\idea_rt.jar=54853 -Dfile.encoding=UTF-8 
java_command: org.codehaus.classworlds.Launcher -Didea.version=2024.3.4 clean install -Dmaven.test.skip -U
java_class_path (initial): C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\plugins\maven\lib\maven3\boot\plexus-classworlds-2.8.0.jar;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.4\plugins\maven\lib\maven3\boot\plexus-classworlds.license
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = **********                                {product} {ergonomic}
   size_t MaxNewSize                               = 2554331136                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = **********                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Python313\Scripts\;C:\Python313\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;C:\Program Files\apache-maven-3.9.9\bin;C:\Program Files\nodejs\;C:\Program Files\Java\jdk-17\bin;;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=LEGION
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 16 days 1:26 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv

Memory: 4k page, system-wide physical 16235M (760M free)
TotalPageFile size 58248M (AvailPageFile size 194M)
current process WorkingSet (physical memory assigned to process): 405M, peak: 414M
current process commit charge ("private bytes"): 480M, peak: 758M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.12+8-LTS-286) for windows-amd64 JRE (17.0.12+8-LTS-286), built on Jun  5 2024 06:46:59 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
