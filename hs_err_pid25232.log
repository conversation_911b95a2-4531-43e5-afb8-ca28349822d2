#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 16777216 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3548), pid=25232, tid=6420
#
# JRE version:  (17.0.12+8) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.12+8-LTS-286, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:65112,suspend=y,server=n --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.sql/java.sql=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED -Dconfig.url=http://stage.kettle.chaayos.com:8888/config-service -Dspring.profiles.active=stage -Denv.type=stage -Dprimary.server=true -Dprimary.channel.partner.server=false -Dhazelcast.discovery.public.ip.enabled=true -Dis.client.node=true -Dclient.node.ip.details=stage.kettle.chaayos.com -Xms1024m -Xmx2048m -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2024.3\captureAgent\debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 com.stpl.tech.kettle.channelpartner.config.ChannelPartnerConfig

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Thu Jun  5 13:10:48 2025 India Standard Time elapsed time: 1.353508 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000001acc8415470):  JavaThread "Unknown thread" [_thread_in_vm, id=6420, stack(0x000000a324700000,0x000000a324800000)]

Stack: [0x000000a324700000,0x000000a324800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67a04a]
V  [jvm.dll+0x7da4ed]
V  [jvm.dll+0x7dbe33]
V  [jvm.dll+0x7dc4a3]
V  [jvm.dll+0x24508f]
V  [jvm.dll+0x677089]
V  [jvm.dll+0x66bd32]
V  [jvm.dll+0x301fa6]
V  [jvm.dll+0x309546]
V  [jvm.dll+0x359d43]
V  [jvm.dll+0x359f5f]
V  [jvm.dll+0x2d9078]
V  [jvm.dll+0x2d9fe4]
V  [jvm.dll+0x7aca21]
V  [jvm.dll+0x367591]
V  [jvm.dll+0x78b999]
V  [jvm.dll+0x3ec83f]
V  [jvm.dll+0x3ee471]
C  [jli.dll+0x5297]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffb45c17a18, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001acc84747d0 GCTaskThread "GC Thread#0" [stack: 0x000000a324800000,0x000000a324900000] [id=5864]
  0x000001acc8486300 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000a324900000,0x000000a324a00000] [id=29720]
  0x000001acc8486c20 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000a324a00000,0x000000a324b00000] [id=2888]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffb45449b67]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001acc8410650] Heap_lock - owner thread: 0x000001acc8415470

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffb45833859]

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (2 events):
Event: 0.013 Loaded shared library C:\Program Files\Java\jdk-17\bin\java.dll
Event: 0.014 Loaded shared library C:\Program Files\Java\jdk-17\bin\zip.dll


Dynamic libraries:
0x00007ff7980a0000 - 0x00007ff7980b0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ffbcf000000 - 0x00007ffbcf266000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffbce5f0000 - 0x00007ffbce6b9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffbcc3a0000 - 0x00007ffbcc76c000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffbcc190000 - 0x00007ffbcc2db000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb8a6c0000 - 0x00007ffb8a6d9000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ffb8a6a0000 - 0x00007ffb8a6bb000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ffbcdab0000 - 0x00007ffbcdb62000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffbcda00000 - 0x00007ffbcdaa9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffbcebd0000 - 0x00007ffbcec76000 	C:\WINDOWS\System32\sechost.dll
0x00007ffbcecb0000 - 0x00007ffbcedc6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffbcdb70000 - 0x00007ffbcdd3a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffbcca30000 - 0x00007ffbcca57000 	C:\WINDOWS\System32\win32u.dll
0x00007ffbcec80000 - 0x00007ffbcecab000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffbb0530000 - 0x00007ffbb07ca000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffbcc770000 - 0x00007ffbcc8a2000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffbcca60000 - 0x00007ffbccb03000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffbc4e30000 - 0x00007ffbc4e3b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffbcd6d0000 - 0x00007ffbcd700000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb8a690000 - 0x00007ffb8a69c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ffb8a600000 - 0x00007ffb8a68e000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ffb45160000 - 0x00007ffb45d40000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ffbce6c0000 - 0x00007ffbce6c8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb81a60000 - 0x00007ffb81a6a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffbbcc10000 - 0x00007ffbbcc46000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffbcea10000 - 0x00007ffbcea84000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffbcaf30000 - 0x00007ffbcaf4a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb8a5f0000 - 0x00007ffb8a5fa000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ffbc9850000 - 0x00007ffbc9a91000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffbce260000 - 0x00007ffbce5e4000 	C:\WINDOWS\System32\combase.dll
0x00007ffbcd550000 - 0x00007ffbcd630000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffbb31d0000 - 0x00007ffbb3209000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffbccd20000 - 0x00007ffbccdb9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffbc56d0000 - 0x00007ffbc570b000 	C:\Program Files\Java\jdk-17\bin\jdwp.dll
0x00007ffbc5690000 - 0x00007ffbc569e000 	C:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ffb8a5c0000 - 0x00007ffb8a5e5000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ffb8a560000 - 0x00007ffb8a578000 	C:\Program Files\Java\jdk-17\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Program Files\Java\jdk-17\bin\server

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:65112,suspend=y,server=n --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.sql/java.sql=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED -Dconfig.url=http://stage.kettle.chaayos.com:8888/config-service -Dspring.profiles.active=stage -Denv.type=stage -Dprimary.server=true -Dprimary.channel.partner.server=false -Dhazelcast.discovery.public.ip.enabled=true -Dis.client.node=true -Dclient.node.ip.details=stage.kettle.chaayos.com -Xms1024m -Xmx2048m -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2024.3\captureAgent\debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.stpl.tech.kettle.channelpartner.config.ChannelPartnerConfig
java_class_path (initial): D:\Channel Partner\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-config\3.1.8\spring-cloud-starter-config-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\3.1.7\spring-cloud-starter-3.1.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\3.1.7\spring-cloud-context-3.1.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.6.1\spring-security-crypto-5.6.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\3.1.7\spring-cloud-commons-3.1.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.11.RELEASE\spring-security-rsa-1.0.11.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.69\bcpkix-jdk15on-1.69.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.69\bcprov-jdk15on-1.69.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcutil-jdk15on\1.69\bcutil-jdk15on-1.69.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-config-client\3.1.8\spring-cloud-config-client-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.6.2\spring-boot-autoconfigure-2.6.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.1\jackson-annotations-2.13.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.1\jackson-databind-2.13.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.1\jackson-core-2.13.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.6.2\spring-boot-starter-tomcat-2.6.2.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.56\tomcat-embed-core-9.0.56.jar;C:\Us
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 1073741824                                {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 1073741824                                {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Python313\Scripts\;C:\Python313\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;C:\Program Files\apache-maven-3.9.9\bin;C:\Program Files\nodejs\;C:\Program Files\Java\jdk-17\bin;;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=LEGION
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 16 days 1:08 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv

Memory: 4k page, system-wide physical 16235M (2054M free)
TotalPageFile size 58248M (AvailPageFile size 2M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 1090M, peak: 1106M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.12+8-LTS-286) for windows-amd64 JRE (17.0.12+8-LTS-286), built on Jun  5 2024 06:46:59 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
