package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.RedisCacheService;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.task.MarkOutOfStockOrderTask;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.InventoryServiceEndPoints;
import com.stpl.tech.kettle.channelpartner.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.SwiggyServiceEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.ActionCategory;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.TransactionDifferenceMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.kettle.UnitProductTaxCodeMap;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyMarkOutOfStockOosData;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyMarkOutOfStockOosItem;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyMarkOutOfStockRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyMarkOutOfStockResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Addon;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Item;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderOutOfStockDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Variant;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl.PartnerOrderFallbackStatusDaoImpl;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl.PartnerOrderFallbackLogDaoImpl;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackStatus;
import com.stpl.tech.kettle.domain.model.ActionRequest;
import com.stpl.tech.kettle.domain.model.InventoryInfo;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderMetadata;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.inventory.service.StockEventService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.service.model.RequestData;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.IdNameList;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;

import javax.jms.JMSException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class OrderValidationServiceImpl implements OrderValidationService {

    private static final Logger LOG = LoggerFactory.getLogger(OrderValidationServiceImpl.class);
    public static final String INVENTORY_DOWN = "Inventory Down";
    public static final String SEND_KNOCK_NOTIFICATION = "send-knock-notification";

    @Value("${send.stock.out.notification:false}")
    private Boolean sendStockOutNotification;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private TaxDataCache taxDataCache;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private TrackService trackService;

    @Autowired
    private StockEventService stockEventService;

    @Autowired
    private RedisPublisher redisPublisher;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private RedisCacheService redisCacheService;

    @Autowired
    private PartnerOrderFallbackStatusDaoImpl partnerOrderFallbackStatusDao;

    @Autowired
    private PartnerOrderFallbackLogDaoImpl partnerOrderFallbackLogDao;

    @Autowired
    private PartnerOrderService partnerOrderService;

    @Autowired
    private PartnerOrderServiceImpl partnerOrderServiceImpl;



    @Override
    public boolean runCommonValidations(Order order, PartnerOrderDetail partnerOrderDetail, Map<Integer, StateTaxVO> partnerProductTaxMap,
                                        UnitPartnerBrandMappingData data, Unit unit,SwiggyService swiggyService) {
		UnitProductTaxCodeMap unitProductTaxCodeMap = getUnitProductTaxCodeMap(data);

		// Verify product prices, product taxes (percentage by tax code and values)
//		checkProductPriceAndTaxes(order, unitProductTaxCodeMap, partnerOrderDetail, partnerProductTaxMap, unit);

		// Validate inventory
//        Dont run inventory checks in testing env
        if(AppUtils.isProd(environmentProperties.getEnvType()) || !environmentProperties.isTestingModeEnabled()){
            return addFallbackLogging(checkInventory(order, partnerOrderDetail, swiggyService),partnerOrderDetail);
        }
        return true ;
	}

    @Override
    public void updateCheckOrderStatus(PartnerOrderDetail partnerOrderDetail, boolean isManual) {
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.CHECKED,
            "Order verification done!", currentTime));
        PartnerOrderStatus currentStatus = partnerOrderDetail.getPartnerOrderStatus();
        partnerOrderDetail.setOrderValid(partnerOrderDetail.getOrderErrors().isEmpty());
        partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CHECKED);
        partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(currentStatus,
            partnerOrderDetail.getPartnerOrderStatus(), isManual, currentTime));
        partnerOrderDetail.setToBeProcessed(partnerOrderDetail.getOrderErrors().stream().allMatch(partnerOrderError ->
            partnerOrderError.getErrorCode().isCanBeProcessed()));
        partnerOrderDetail.setToBeRejected(partnerOrderDetail.getOrderErrors().stream().anyMatch(partnerOrderError ->
        partnerOrderError.getErrorCode().isToBeRejected()));
        trackService.updatePartnerOrder(partnerOrderDetail);
    }

    @Override
    public UnitProductTaxCodeMap getUnitProductTaxCodeMap(UnitPartnerBrandMappingData data) {
        /*UnitRegion unitRegion = masterDataCache.getUnit(unitId).getRegion();
        List<UnitBasicDetail> codUnits = masterDataCache.getUnits(UnitCategory.COD);
        Optional<UnitBasicDetail> unit = codUnits.stream().filter(unit1 -> unitRegion.equals(unit1.getRegion()) &&
                unit1.isPartnerPriced()).findAny();*/
        Collection<ProductVO> products = masterDataCache.getUnitProductTrimmedDetails(data.getPriceProfileUnitId());
        Map<Integer, ProductVO> productMap = new HashMap<>();
        UnitProductTaxCodeMap unitProductTaxCodeMap = new UnitProductTaxCodeMap();
        products.forEach(productVO -> productMap.put(productVO.getId(), productVO));
        unitProductTaxCodeMap.setProductVOMap(productMap);
        getResult(masterDataCache.getUnit(data.getUnitId()), unitProductTaxCodeMap);
        return unitProductTaxCodeMap;
    }

    @Override
    public void addOrderError(PartnerOrderDetail partnerOrderDetail, PartnerOrderErrorCode errorCode, String errorDescription) {
        PartnerOrderError partnerOrderError = new PartnerOrderError();
        partnerOrderError.setErrorCode(errorCode);
        partnerOrderError.setErrorDescription(errorDescription);
        partnerOrderDetail.getOrderErrors().add(partnerOrderError);
    }

    @Override
    public boolean unitCanDeliver(Integer unitId) {
        List<UnitHours> ubs = masterDataCache.getOperationalHoursForUnit(unitId);
        Date date = AppUtils.getCurrentTimestamp();
        int dayOfWeek = AppUtils.getDayOfWeek(date);
        for (UnitHours uhrs : ubs) {
            if (uhrs.getDayOfTheWeek() != null && uhrs.getDayOfTheWeek().trim() != ""
                && uhrs.getDayOfTheWeekNumber() == dayOfWeek && uhrs.isIsOperational()) {
                boolean valid = compareTime(uhrs.getDeliveryOpeningTime().toString(), uhrs.getDeliveryClosingTime().toString(), date);
                if (!valid) {
                    Unit unit = masterDataCache.getUnit(unitId);
                    String msg = "ORDER OUTSIDE DELIVERY HOURS: \nCAFE: " + unit.getName() +
                        "\n DELIVERY HOURS:\n START TIME:" + uhrs.getDeliveryOpeningTime() + " END TIME" + uhrs.getDeliveryClosingTime();
                    SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(), AppConstants.KETTLE,
                        SlackNotification.PARTNER_INTEGRATION, msg);
                }
                return valid;
            }
        }
        return false;
    }

    @Override
    public void checkProductPriceAndTaxes(Order order, UnitProductTaxCodeMap unitProductTaxCodeMap, PartnerOrderDetail partnerOrderDetail,
                                          Map<Integer, StateTaxVO> partnerProductTaxMap, Unit unit) {

        BigDecimal cartValue = BigDecimal.ZERO;
        for (OrderItem item : order.getOrders()) {
            if (item.getProductId() != ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID) { //packaging charge product
                cartValue = ChannelPartnerUtils.add(cartValue, ChannelPartnerUtils.multiply(item.getPrice(), new BigDecimal(item.getQuantity())));
                if (partnerOrderDetail.getPartnerName().equalsIgnoreCase("ZOMATO") && item.getDiscountDetail() != null) {
                    cartValue = ChannelPartnerUtils.subtract(cartValue, item.getDiscountDetail().getTotalDiscount());
                }
            }
        }
        BigDecimal packagingCharge;
        if (unit.getPackagingType().equalsIgnoreCase("PERCENTAGE")) {
            packagingCharge = ChannelPartnerUtils.multiply(ChannelPartnerUtils.divide(unit.getPackagingValue(), BigDecimal.valueOf(100)), cartValue);
        } else {
            packagingCharge = unit.getPackagingValue();
        }
/*        for (OrderItem orderItem : order.getOrders()) {
            boolean dimensionMatched = false;
            Integer productId = orderItem.getProductId();
            String productName = orderItem.getProductName() + "[" + productId + "] ";
            for (ProductPriceVO productPriceVO : unitProductTaxCodeMap.getProductVOMap().get(productId).getPrices()) {
                boolean isSuperComboOrHeroCombo = ChannelPartnerUtils.isSuperComboOrHeroCombo(orderItem.getProductSubCategory().getId());
                if (productPriceVO.getDimension().equalsIgnoreCase(orderItem.getDimension())) {
                    dimensionMatched = true;
                    BigDecimal price = productPriceVO.getPrice();
                    if (ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID.equals(productId)) {
                        price = packagingCharge.setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    if (orderItem.getOriginalPrice() != null && price.compareTo(orderItem.getOriginalPrice()) != 0 && !isSuperComboOrHeroCombo) {
                        logMetadataDifference(order, "ITEM", "PRICE", productId, productPriceVO.getPrice().floatValue(),
                            orderItem.getPrice().floatValue());
                        addOrderError(partnerOrderDetail, PartnerOrderErrorCode.PRICE_MISMATCH, "Price mismatch " + productName +
                            "our/partner: " + price + "/" + orderItem.getOriginalPrice());
                        LOG.error("Item Price mismatch for " + productName);
                    }
                    StateTaxVO stateTaxVO = partnerProductTaxMap.get(productId);
                    if (stateTaxVO != null && !isSuperComboOrHeroCombo) { //stateTaxVO is null for Zomato where we do not get tax bifurcations
                        for (TaxDetail taxDetail : orderItem.getTaxes()) {
                            if (taxDetail.getCode().equalsIgnoreCase("CGST")) {
                                if (taxDetail.getPercentage().compareTo(stateTaxVO.getCgst()) != 0) {
                                    logMetadataDifference(order, "ITEM", "CGST", productId, stateTaxVO.getCgst().floatValue(),
                                        taxDetail.getPercentage().floatValue());
                                    addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TAX_MISMATCH, "Tax percent mismatch " +
                                        productName + "type CGST our/partner: " + taxDetail.getPercentage() + "/" + stateTaxVO.getCgst());
                                    LOG.error("Item Tax mismatch for " + productName + "type CGST");
                                }
                            } else if (taxDetail.getCode().equalsIgnoreCase("SGST/UTGST")) {
                                if (taxDetail.getPercentage().compareTo(stateTaxVO.getSgst()) != 0) {
                                    logMetadataDifference(order, "ITEM", "SGST", productId, stateTaxVO.getSgst().floatValue(),
                                        taxDetail.getPercentage().floatValue());
                                    addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TAX_MISMATCH, "Tax percent mismatch " +
                                        productName + "type SGST our/partner: " + taxDetail.getPercentage() + "/" + stateTaxVO.getSgst());
                                    LOG.error("Item Tax mismatch for " + productName + "[" + productId + "] type SGST");
                                }
                            } else if (taxDetail.getCode().equalsIgnoreCase("IGST")) {
                                if (taxDetail.getPercentage().compareTo(stateTaxVO.getIgst()) != 0) {
                                    logMetadataDifference(order, "ITEM", "IGST", productId, stateTaxVO.getIgst().floatValue(),
                                        taxDetail.getPercentage().floatValue());
                                    addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TAX_MISMATCH, "Tax percent mismatch " +
                                        productName + "type IGST our/partner: " + taxDetail.getPercentage() + "/" + stateTaxVO.getIgst());
                                    LOG.error("Item Tax mismatch for " + productName + "type IGST");
                                }
                            }
                        }
                    }
                }
            }
            if (!dimensionMatched) {
                addOrderError(partnerOrderDetail, PartnerOrderErrorCode.SIZE_MISMATCH, "Size " + orderItem.getDimension() +
                    " for product " + productName + "[" + productId + "] not available for ordering.");
                LOG.error("Item size mismatch for " + productName + orderItem.getDimension());
            }
        }
        publishPriceMismatchToSlack(partnerOrderDetail);*/
    }

    private void setQuantityToZero(Integer productId,String dimension ,Boolean haveDimension , Map<Integer,InventoryInfo> aggregatedProductQty){
        if(Boolean.TRUE.equals(haveDimension)){
            InventoryInfo currentStock = aggregatedProductQty.get(productId);
            currentStock.getDim().put(dimension,0);
            aggregatedProductQty.put(productId,currentStock);
        }else {
            InventoryInfo currentStock = aggregatedProductQty.get(productId);
            currentStock.setQuantity(0);
            aggregatedProductQty.put(productId,currentStock);
        }
    }


    public boolean addFallbackLogging(boolean result, PartnerOrderDetail partnerOrderDetail) {
        try {
            Boolean fallbackOrder = partnerOrderDetail.getOrderErrors().stream().anyMatch(partnerOrderError ->
                    !partnerOrderError.getErrorCode().isCanBeProcessed());
            if (fallbackOrder) {
                partnerOrderServiceImpl.logFallbackData(partnerOrderDetail,String.valueOf(PartnerActionCode.CHECKED),"Order Checked",ActionCategory.KETTLE_ORDER_CHECKED.name(),null,String.valueOf(AppConstants.SYSTEM_EMPLOYEE_ID));
            }
        } catch (Exception e) {
            return result;
        }
        return result;
    }


    @Override
	public boolean checkInventory(Order order, PartnerOrderDetail partnerOrderDetail,SwiggyService swiggyService) {
        List<OrderItem> outOfStockItems = new ArrayList<>();
		IdNameList data = new IdNameList();
		data.setId(order.getUnitId());
		data.setName(order.getUnitName());
        Map<Integer, InventoryInfo> aggregatedProductQty = new HashMap<>();
		order.getOrders().forEach(orderItem -> {
			if (orderItem.getProductId() != ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID
					&& orderItem.getProductId() != ChannelPartnerServiceConstants.DELIVERY_PRODUCT_ID) {
				if (orderItem.getProductCategory().getId() == 8 || orderItem.getProductCategory().getId() == 43) {
					orderItem.getComposition().getMenuProducts().forEach(orderItem1 -> {
						if (masterDataCache.getProduct(orderItem1.getProductId()).isInventoryTracked()) {
							data.getList().add(orderItem1.getProductId());
						}
					});
				} else {
					if (masterDataCache.getProduct(orderItem.getProductId()).isInventoryTracked()) {
						data.getList().add(orderItem.getProductId());
					}
				}
			}
		});
		Set<Integer> stockOutList = new HashSet<>();
		Map<String, InventoryInfo> map = null;
		if (!data.getList().isEmpty()) {
			map = getUnitProductLiveInventoryByProducts(order.getUnitId(), data.getList());
		}
//        Map<String, InventoryInfo> map = getUnitProductLiveInventoryByProducts(order.getUnitId(), data.getList());
		if (map != null && !map.isEmpty()) {
            LOG.info("Inventory map For Partner Order :: {} :::: {} ",partnerOrderDetail.getPartnerOrderId() , new Gson().toJson(map));
			Map<Integer, InventoryInfo> productStock = new HashMap<>();
			for (Map.Entry<String, InventoryInfo> entry : map.entrySet()) {
				String stock = new Gson().toJson(entry.getValue());
				productStock.put(Integer.valueOf(entry.getKey()), new Gson().fromJson(stock, InventoryInfo.class));
			}
            order.getOrders().forEach(orderItem -> {
                if (orderItem.getProductId() != ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID
                        && orderItem.getProductId() != ChannelPartnerServiceConstants.DELIVERY_PRODUCT_ID) {
                    if (orderItem.getProductCategory().getId() == 8 || orderItem.getProductCategory().getId() == 43) {
                        orderItem.getComposition().getMenuProducts().forEach(orderItem1 -> {
                            if (masterDataCache.getProduct(orderItem1.getProductId()).isInventoryTracked()) {
                                InventoryInfo stock = productStock.get(orderItem1.getProductId());
                                if(!aggregatedProductQty.containsKey(orderItem1.getProductId()) && Objects.nonNull(stock)){
                                    aggregatedProductQty.put(orderItem1.getProductId(),stock);
                                }
                            }
                        });
                    } else {
                        if (masterDataCache.getProduct(orderItem.getProductId()).isInventoryTracked()) {
                            InventoryInfo stock = productStock.get(orderItem.getProductId());
                            if(!aggregatedProductQty.containsKey(orderItem.getProductId()) && Objects.nonNull(stock)){
                                aggregatedProductQty.put(orderItem.getProductId(),stock);
                            }
                        }
                    }
                }
            });
			order.getOrders().forEach(orderItem -> {
				if (orderItem.getProductCategory().getId() == 8 || orderItem.getProductCategory().getId() == 43) {
                    Map<Integer, Integer> comboProductQty = new HashMap<>();
                    orderItem.getComposition().getMenuProducts().forEach(comboItem ->{
                        boolean inventoryTracked = masterDataCache.getProduct(comboItem.getProductId())
                                .isInventoryTracked();
                        if (inventoryTracked) {
                            if(comboProductQty.containsKey(comboItem.getProductId())){
                                Integer currentQty = comboProductQty.get(comboItem.getProductId());
                                comboProductQty.put(comboItem.getProductId(),currentQty + comboItem.getQuantity());
                            }else{
                                comboProductQty.put(comboItem.getProductId(),comboItem.getQuantity());
                            }
                        }
                    });

					orderItem.getComposition().getMenuProducts().forEach(orderItem1 -> {
						boolean inventoryTracked = masterDataCache.getProduct(orderItem1.getProductId())
								.isInventoryTracked();
						if (inventoryTracked) {
                            if(comboProductQty.get(orderItem1.getProductId()) == -1){
                                return;
                            }
                            Boolean haveDimension = false;
							Integer qty = orderItem.getQuantity() * comboProductQty.get(orderItem1.getProductId());
							//InventoryInfo stock = productStock.get(orderItem1.getProductId());
                            //Integer aggregatedStockQty = aggregatedProductQty.get(orderItem1.getProductId());
                            InventoryInfo stock = aggregatedProductQty.get(orderItem1.getProductId());
							Integer stockQty = null;
							if (stock != null) {
								stockQty = stock.getQuantity();
								if (stock.getDim() != null && stock.getDim().containsKey(orderItem1.getDimension())) {
									stockQty = stock.getDim().get(orderItem1.getDimension());
                                    haveDimension = true;
								}
							}
							if (stockQty == null || (stockQty > 0 && stockQty < qty)) {
								addOrderError(partnerOrderDetail, PartnerOrderErrorCode.STOCK_NOT_SUFFICIENT,
										"Combo Product " + orderItem.getProductName() + " - constituent:"
												+ orderItem1.getProductName() + " short in stock requested/available "
												+ qty + "/" + stockQty);
                                outOfStockItems.add(orderItem);
                                setQuantityToZero(orderItem1.getProductId(),orderItem1.getDimension(),haveDimension,aggregatedProductQty);
							}

							if (stockQty == null || (stockQty == 0 && stockQty < qty)) {
								addOrderError(partnerOrderDetail, PartnerOrderErrorCode.STOCK_NOT_AVAILABLE,
										"Combo Product " + orderItem.getProductName() + " - constituent:"
												+ orderItem1.getProductName() + " short in stock requested/available "
												+ qty + "/" + stockQty);
                                outOfStockItems.add(orderItem);
                                setQuantityToZero(orderItem1.getProductId(),orderItem1.getDimension(),haveDimension,aggregatedProductQty);

                            }
                            if(aggregatedProductQty.containsKey(orderItem1.getProductId())){
                                Integer currentQty ;
                                Integer updatedQuantity;
                                InventoryInfo currentStock = aggregatedProductQty.get(orderItem1.getProductId());
                                if (currentStock != null) {
                                    currentQty = currentStock.getQuantity();
                                    if (currentStock.getDim() != null && currentStock.getDim().containsKey(orderItem1.getDimension())) {
                                        currentQty = currentStock.getDim().get(orderItem1.getDimension());
                                        updatedQuantity = currentQty -  (orderItem.getQuantity() * comboProductQty.get(orderItem1.getProductId()));
                                        currentStock.getDim().put(orderItem1.getDimension(),updatedQuantity);
                                    }else{
                                        updatedQuantity = currentQty -  (orderItem.getQuantity() * comboProductQty.get(orderItem1.getProductId()));
                                        currentStock.setQuantity(updatedQuantity);
                                    }
                                }
                                aggregatedProductQty.put(orderItem1.getProductId(),currentStock);
                            }
                            comboProductQty.put(orderItem1.getProductId(),-1);
						}
					});
				} else {
					InventoryInfo stock1 = productStock.get(orderItem.getProductId());
                    //Integer aggregatedStockQty = aggregatedProductQty.get(orderItem.getProductId());
					boolean inventoryTracked = masterDataCache.getProduct(orderItem.getProductId())
							.isInventoryTracked();
					if (inventoryTracked && (stock1 == null)) {
						String errorMessage = "Product " + orderItem.getProductName()
								+ " stock not found requested/available " + orderItem.getQuantity() + "/none";
						addOrderError(partnerOrderDetail, PartnerOrderErrorCode.STOCK_NOT_FOUND, errorMessage);
                        outOfStockItems.add(orderItem);
						LOG.error(errorMessage);
					}else if(inventoryTracked){
                        Boolean haveDimension = false;
                        InventoryInfo stock = aggregatedProductQty.get(orderItem.getProductId());
                        Integer stockQty = null;
                        if (stock != null) {
                            stockQty = stock.getQuantity();
                            if (stock.getDim() != null && stock.getDim().containsKey(orderItem.getDimension())) {
                                stockQty = stock.getDim().get(orderItem.getDimension());
                                haveDimension = true;
                            }
                        }
                        /*Integer stockQty = stock.getQuantity();
                        if (stock.getDim() != null && stock.getDim().containsKey(orderItem.getDimension())) {
                            stockQty = stock.getDim().get(orderItem.getDimension());
                        }*/
                        if ((stockQty > 0 &&  stockQty < orderItem.getQuantity())) {
                            addOrderError(partnerOrderDetail, PartnerOrderErrorCode.STOCK_NOT_SUFFICIENT,
                                    "Product " + orderItem.getProductName() + " short in stock requested/available "
                                            + orderItem.getQuantity() + "/" + stockQty);
                            outOfStockItems.add(orderItem);
                            setQuantityToZero(orderItem.getProductId(),orderItem.getDimension(),haveDimension,aggregatedProductQty);

                        }

                        if ((stockQty == 0 && stockQty < orderItem.getQuantity())) {
                            addOrderError(partnerOrderDetail, PartnerOrderErrorCode.STOCK_NOT_AVAILABLE,
                                    "Product " + orderItem.getProductName() + " short in stock requested/available "
                                            + orderItem.getQuantity() + "/" + stockQty);
                            outOfStockItems.add(orderItem);
                            setQuantityToZero(orderItem.getProductId(),orderItem.getDimension(),haveDimension,aggregatedProductQty);
                        }
                        if(aggregatedProductQty.containsKey(orderItem.getProductId())){
                            Integer currentQty ;
                            Integer updatedQuantity;
                            InventoryInfo currentStock = aggregatedProductQty.get(orderItem.getProductId());
                            if (currentStock != null) {
                                currentQty = currentStock.getQuantity();
                                    if (currentStock.getDim() != null && currentStock.getDim().containsKey(orderItem.getDimension())) {
                                        currentQty = currentStock.getDim().get(orderItem.getDimension());
                                        if(currentQty>0){
                                            updatedQuantity = currentQty -  orderItem.getQuantity();
                                            currentStock.getDim().put(orderItem.getDimension(),updatedQuantity);
                                        }
                                    }else if (currentQty>0){
                                        updatedQuantity = currentQty -  orderItem.getQuantity();
                                        currentStock.setQuantity(updatedQuantity);
                                    }

                            }
                            aggregatedProductQty.put(orderItem.getProductId(),currentStock);
                        }
                    }
				}
			});
			List<IdName> dimensionStockOutList = new ArrayList<>();
			productStock.keySet().forEach(key -> {
				Integer productId = Integer.valueOf(key.toString());
				boolean inventoryTracked = masterDataCache.getProduct(productId).isInventoryTracked();
				if (inventoryTracked) {
					InventoryInfo stock = productStock.get(productId);
					Integer stockQty = stock.getQuantity();
					if (stockQty <= 0) {
						stockOutList.add(productId);
					}
					if (stock.getDim() != null) {
						stock.getDim().keySet().forEach(dimension -> {
							Integer dimQty = stock.getDim().get(dimension);
							if (dimQty == null || dimQty <= 0) {
								dimensionStockOutList.add(new IdName(productId, dimension));
							}
						});
					}
				}
			});
			if (!stockOutList.isEmpty()) {
				UnitProductsStockEvent event = new UnitProductsStockEvent();
				event.setStatus(StockStatus.STOCK_OUT);
				event.setUnitId(order.getUnitId());
				event.setProductDimensions(dimensionStockOutList);
				for (Integer productId : stockOutList) {
					event.getProductIds().add(productId.toString());
				}
				try {
					stockEventService.publishStockEvent(environmentProperties.getEnvType().name(), event);
				} catch (JMSException e) {
					LOG.info("Error publishing stock event::::::::::: {}", new Gson().toJson(event));
					LOG.error("Error publishing stock event:::::::::::", e);
				}
			}
			if (partnerOrderDetail.getOrderErrors() != null && partnerOrderDetail.getOrderErrors().size() > 0) {
				for (PartnerOrderError error : partnerOrderDetail.getOrderErrors()) {
					if ((error.getErrorCode().equals(PartnerOrderErrorCode.STOCK_NOT_AVAILABLE)
							|| error.getErrorCode().equals(PartnerOrderErrorCode.STOCK_NOT_SUFFICIENT)
							|| error.getErrorCode().equals(PartnerOrderErrorCode.STOCK_NOT_FOUND))
							&& error.getErrorCode().isToBeRejected()) {
						return false;
					}
				}
			}
            //Move Mark Out of Stock to a Diffferent Thread
            if (partnerOrderDetail.getPartnerName().equalsIgnoreCase("SWIGGY")){
                if(!outOfStockItems.isEmpty()){
                    Stream<Integer> productIds =  outOfStockItems.stream().map(OrderItem::getProductId);
                    partnerOrderDetail.setStockOutProductIds(productIds.collect(Collectors.toList()));
                }
                if(Boolean.TRUE.equals(sendStockOutNotification)){
                    setOutOfStockProcessingThread(partnerOrderDetail, order, outOfStockItems, swiggyService);
                }

            }
/*
            if (!outOfStockItems.isEmpty()) {
                try {
                    List<String> outOfStockItemsForSwiggy = getOutOfStockItems(partnerOrderDetail, outOfStockItems);
                    markOutOfStockItemsForSwiggy(partnerOrderDetail, outOfStockItemsForSwiggy);
                } catch (Exception e) {
                    LOG.error("Error marking item OOS Swiggy", e);
                }
            }*/
			return true;

		} else if (map != null) {
            sendInventoryDownNotificationToKnock(order.getUnitId(), order.getCustomerId());
			sendInventoryDownNotification(order.getUnitId(), order.getCustomerId());
			LOG.info("Inventory service is down and we are punching this order");
			return true;
		}
		return true;
    }

    private void setOutOfStockProcessingThread(PartnerOrderDetail partnerOrderDetail, Order order,List<OrderItem> outOfStockItems ,SwiggyService swiggyService) {
        MarkOutOfStockOrderTask markOutOfStockOrderTask = new MarkOutOfStockOrderTask();
        markOutOfStockOrderTask.setOrderValidationService(this);
        markOutOfStockOrderTask.setSwiggyService(swiggyService);
        markOutOfStockOrderTask.setOrder(order);
        markOutOfStockOrderTask.setOutOfStockItems(outOfStockItems);
        markOutOfStockOrderTask.setPartnerOrderDetail(partnerOrderDetail);
        markOutOfStockOrderTask.setSwiggyOrderRequest((SwiggyOrderRequest)partnerOrderDetail.getPartnerOrder());
        markOutOfStockOrderTask.setRequestId(MDC.get(generateID()));
        LOG.info("Before sending out of stock API CALL TO A DIFFERENT THREAD  :::::::::");
        threadPoolTaskExecutor.execute(markOutOfStockOrderTask);
    }

    public  String generateID() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString();
    }

    private List<String> getOutOfStockItems(PartnerOrderDetail partnerOrderDetail, List<OrderItem> items) {
        List<String> externalIds = new ArrayList<>();
        List<String> addOnIds = new ArrayList<>();
        List<String> itemIds= new ArrayList<>();
        List<String> variantIds = new ArrayList<>();
        if (partnerOrderDetail.getPartnerName().equals("SWIGGY")) {
            LOG.info("ENtered get Out of stock items method :::::::::::::::");
            //For Item Stockout
            SwiggyOrderRequest swiggyOrderRequest = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
            for (Item item : swiggyOrderRequest.getItems()) {
                String swiggyGeneratedItemId = item.getId();
                for (OrderItem orderItem : items) {
                    if (Integer.toString(orderItem.getProductId()).equals(swiggyGeneratedItemId.split("_")[0])) {
                        externalIds.add(swiggyGeneratedItemId);
                        itemIds.add(swiggyGeneratedItemId);
                    }
                }
            }
            //For ADDons Stock out
            for (Item item : swiggyOrderRequest.getItems()) {
                for(Addon addon :item.getAddons()){
                    String swiggyGeneratedItemId = addon.getId();
                    for (OrderItem orderItem : items) {
                        if (Integer.toString(orderItem.getProductId()).equals(swiggyGeneratedItemId.split("_")[0])) {
                            externalIds.add(swiggyGeneratedItemId);
                            addOnIds.add(swiggyGeneratedItemId);
                        }
                    }
                }
            }

            //for VariantsStock out
            for (Item item : swiggyOrderRequest.getItems()) {
                for(Variant variant :item.getVariants()){
                    String swiggyGeneratedItemId = variant.getId();
                    for (OrderItem orderItem : items) {
                        if (Integer.toString(orderItem.getProductId()).equals(swiggyGeneratedItemId.split("_")[0])) {
                            externalIds.add(swiggyGeneratedItemId);
                            variantIds.add(swiggyGeneratedItemId);
                        }
                    }
                }
            }
        }
        LOG.info("Preparing List of out of stock item Ids ::::::::{}", externalIds);
        SwiggyOrderOutOfStockDetail swiggyOrderOutOfStockDetail = new SwiggyOrderOutOfStockDetail();
        swiggyOrderOutOfStockDetail.setPartnerOrderId(partnerOrderDetail.getPartnerOrderId());
        swiggyOrderOutOfStockDetail.setItemIds(itemIds);
        swiggyOrderOutOfStockDetail.setAddOnIds(addOnIds);
        swiggyOrderOutOfStockDetail.setVariantIds(variantIds);
        redisCacheService.saveSwiggyOrderOutOfStockData(swiggyOrderOutOfStockDetail);
        return externalIds;
    }

    private void markOutOfStockItemsForSwiggy(PartnerOrderDetail partnerOrderDetail, List<String> outOfStockItemId,SwiggyService swiggyService) {
        if (partnerOrderDetail.getPartnerName().equals("SWIGGY") && outOfStockItemId.size() > 0) {
            SwiggyMarkOutOfStockRequest markOutOfStockRequest = new SwiggyMarkOutOfStockRequest();
            markOutOfStockRequest.setOrderId(partnerOrderDetail.getPartnerOrderId());
            markOutOfStockRequest.setExternalRestaurantId(partnerOrderDetail.getRestaurantId());
            markOutOfStockRequest.setEditAllowed("true");
            markOutOfStockRequest.setOosData(new ArrayList<>());
            if(Objects.nonNull(markOutOfStockRequest.getOosData())) {
                for (String itemId : outOfStockItemId) {
                    SwiggyMarkOutOfStockOosItem item = new SwiggyMarkOutOfStockOosItem();
                    SwiggyMarkOutOfStockOosData swiggyMarkOutOfStockOosData = new SwiggyMarkOutOfStockOosData();
                    item.setItemId(itemId);
                    swiggyMarkOutOfStockOosData.setItem(item);
                    markOutOfStockRequest.getOosData().add(swiggyMarkOutOfStockOosData);
                }
            }
            PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId(partnerOrderDetail.getPartnerOrderId());
            partnerOrderServiceImpl.logFallbackData(partnerOrderDetail,String.valueOf(PartnerActionCode.SWIGGY_CALL_INITIATED),"Stock Out Notificcation Sent ",ActionCategory.BUTTON_CLICKED.name(),statusLogPrev,partnerOrderDetail.getFallbackProcessedBy());
            LOG.info("Swiggy out of Stock Request :::::::::::::::{}", new Gson().toJson(markOutOfStockRequest));
            SwiggyMarkOutOfStockResponse response = new SwiggyMarkOutOfStockResponse();
            try {
                response = webServiceHelper.callSwiggyApi(environmentProperties,
                        SwiggyServiceEndpoints.MARK_OUT_OF_STOCK, HttpMethod.POST, markOutOfStockRequest,
                        SwiggyMarkOutOfStockResponse.class);
                LOG.info("Response of out of stpck api from swigggy ::::::{}", new Gson().toJson(response));
                if (response == null || response.getStatusCode() != 0) {
                    String responseJson = new Gson().toJson(response);
                    LOG.info("Error in updating out of stock status on SWIGGY:::: {}", responseJson);
                }else{
                    LOG.info("Swiggy out of Stock Response :::::::::::::::{}", new Gson().toJson(response));
                    swiggyService.runOOSItemsStockInTask(partnerOrderDetail.getPartnerOrderId(),new UnitPartnerBrandKey(
                            partnerOrderDetail.getUnitId(),  partnerOrderDetail.getBrandId(),6));
                }
            } catch (HttpStatusCodeException e) {
                LOG.error("Error in updating unit status on SWIGGY:::: {}", e.getResponseBodyAsString());
            }
        }
    }

    @Override
    public boolean isValidData(BigDecimal value1, BigDecimal value2) {
        return ChannelPartnerUtils.isEqual(value1, value2) || isAcceptableVariance(value1, value2);
    }

    @Override
    public void logMetadataDifference(Order order, String type, String param, Integer id, Float expected, Float actual) {
        OrderMetadata orderMetadata = new OrderMetadata();
        orderMetadata.setAttributeName("TXN_DIFF");
        orderMetadata.setAttributeValue(new Gson().toJson(new TransactionDifferenceMetadata(type, param, id, expected, actual)));
        order.getMetadataList().add(orderMetadata);
    }

    @Override
    public void tagOrder(PartnerOrderDetail partnerOrderDetail, Order order) {
        boolean superComboCheck = true;
        for (OrderItem item : order.getOrders()) {
            if (item.getProductSubCategory().getId() == 3675 || item.getProductSubCategory().getId() == 3676) { // Hero and Super Combo check
                superComboCheck = false;
            }
        }
        try {
            if (partnerOrderDetail.getBillDifference().compareTo(BigDecimal.ONE) > 0 && superComboCheck) {
                OrderMetadata orderMetadata = new OrderMetadata();
                orderMetadata.setAttributeName("EXCEEDED_THRESHOLD");
                order.getMetadataList().add(orderMetadata);
                if (partnerOrderDetail.getBillPercentageDifference().compareTo(BigDecimal.valueOf(5)) >= 0) {
                    PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
                    partnerActionEvent.setEventType(PartnerActionEventType.ORDER_MISMATCH);
                    partnerActionEvent.setEventData(partnerOrderDetail);
                    partnerActionEvent.setPartner(false);
                    redisPublisher.publish("COMMON", new Gson().toJson(partnerActionEvent));
                }
            }
        }catch (Exception e){
            LOG.info("Error While Sending Event For Order Mismatch");
        }
    }

    @Override
    public void checkPaidAmount(Order order, PartnerOrderDetail partnerOrderDetail, TransactionDetail td1, BigDecimal paidAmount) {
        BigDecimal ourPaidAmount = ChannelPartnerUtils.subtract(td1.getCollectionAmount(), td1.getRoundOffValue());
        logMetadataDifference(order, "CART", "PAIDAMT", null, ourPaidAmount.floatValue(),
            paidAmount.floatValue());
        addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
            "Paid Amount our/partner " + td1.getCollectionAmount() + "/" + paidAmount);
        partnerOrderDetail.setBillDifference(ChannelPartnerUtils.subtract(ourPaidAmount, paidAmount).abs());
        partnerOrderDetail.setBillPercentageDifference(ChannelPartnerUtils.percentage(partnerOrderDetail.getBillDifference(), ourPaidAmount));
        if (partnerOrderDetail.getBillDifference().compareTo(BigDecimal.valueOf(1)) > 0) {
            publishTransactionMismatchToSlack(partnerOrderDetail);
        }
    }

    @Override
    public void updatePartnerNotificationStatus(PartnerOrderDetail partnerOrderDetail, boolean success, Boolean isManual) throws ChannelPartnerException {
        if (success) {
            partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.ORDER_CONFIRMED,
                "Confirmed " + partnerOrderDetail.getPartnerName() + " order!", null));
            partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                PartnerOrderStatus.NOTIFIED, isManual, null));
            //partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.NOTIFIED);
            partnerOrderDetail.setBeingProcessed(false);
            partnerOrderDetail.setNotified(true);
            trackService.updatePartnerOrder(partnerOrderDetail);
        } else {
            partnerOrderDetail.setBeingProcessed(false);
            trackService.updatePartnerOrder(partnerOrderDetail);
        }
    }

    @Override
    public void refreshUnitInventory(Integer unitId,
                                     UnitProductsStockEvent stockIn, UnitProductsStockEvent stockOut) {
        Map<String, Integer> inventory = webServiceHelper.callInternalApi(environmentProperties.getKettleServiceBasePath() +
                KettleServiceClientEndpoints.UNIT_INVENTORY_WEB, environmentProperties.getChannelPartnerClientToken(),
            HttpMethod.POST, Map.class, unitId, null);
        if (inventory != null && !inventory.keySet().isEmpty()) {
            for (String productId : inventory.keySet()) {
                int stock = inventory.get(productId);
                if (stock > 0) {
                    stockIn.getProductIds().add(productId);
                } else {
                    stockOut.getProductIds().add(productId);
                }
            }
        }
    }

    @Override
    public void refreshLiveUnitInventory(Integer unitId,
                                         UnitProductsStockEvent stockIn, UnitProductsStockEvent stockOut) {
        Map<String, InventoryInfo> inventory = webServiceHelper.callInternalApi(environmentProperties.getKettleServiceBasePath() +
                KettleServiceClientEndpoints.UNIT_INVENTORY_LIVE_WEB, environmentProperties.getChannelPartnerClientToken(),
            HttpMethod.POST, Map.class, unitId, null);
        if (inventory != null && !inventory.keySet().isEmpty()) {
            for (String productId : inventory.keySet()) {
                InventoryInfo inventoryInfo = new Gson().fromJson(new Gson().toJson(inventory.get(productId)), InventoryInfo.class);
                int stock = inventoryInfo.getQuantity();
                if (stock > 0) {
                    stockIn.getProductIds().add(productId);
                } else {
                    stockOut.getProductIds().add(productId);
                }
                if(inventoryInfo.getDim() != null) {
                    inventoryInfo.getDim().forEach((dimension, stockCount) -> {
                        if (stockCount > 0) {
                            stockIn.getProductDimensions().add(new IdName(Integer.parseInt(productId), dimension));
                        } else {
                            stockOut.getProductDimensions().add(new IdName(Integer.parseInt(productId), dimension));
                        }
                    });
                }
            }
        }
    }

    @Override
    public Map getUnitProductInventoryByProducts(Integer unitId, List<Integer> productIds) {
        IdNameList data = new IdNameList();
        data.setId(unitId);
        data.getList().addAll(productIds);
        return webServiceHelper.postWithAuth(environmentProperties.getKettleServiceBasePath() +
            KettleServiceClientEndpoints.UNIT_PRODUCT_INVENTORY_STATUS, environmentProperties.getChannelPartnerClientToken(), data, Map.class);
    }

    @Override
    public Map<String, InventoryInfo> getUnitProductLiveInventoryByProducts(Integer unitId, List<Integer> productIds) {
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        try {
            IdNameList data = new IdNameList();
            data.setId(unitId);
            data.getList().addAll(productIds);
            LOG.info("Getting Product Inventory details for unit Id {} for Web Service", data.getId());
            LOG.info("Product Ids For Inventory Fetch :::: {} ",data.getList());
            HashMap<String, InventoryInfo> inventoryInfoMap = new HashMap<>();
            List<InventoryInfo> inventoryInfoList = getTrimmedDownUnitInventory(data.getId(), data.getList());
            if (environmentProperties.callInventoryForWebOrders()) {
                if (Objects.nonNull(inventoryInfoList)) {
                    for (InventoryInfo info : inventoryInfoList) {
                        inventoryInfoMap.put(Integer.toString(info.getId()), info);
                    }
                    return inventoryInfoMap;
                } else {
                    return getUnitProductInventoryForWeb(unitId, productIds, data);
                }
            } else {
                return webServiceHelper.postWithAuth(environmentProperties.getKettleServiceBasePath() +
                                KettleServiceClientEndpoints.UNIT_PRODUCT_INVENTORY_LIVE_STATUS, environmentProperties.getChannelPartnerClientToken(),
                        data, Map.class);
            }
        } catch (Exception e) {
            LOG.error("Not able to get inventory data for unit {}", unitId, e);
            return new HashMap<>();
        }
    }

    private Map<String, InventoryInfo> getUnitProductInventoryForWeb(Integer unitId, List<Integer> productIds, IdNameList data) {
        try{
            return webServiceHelper.postWithAuth(environmentProperties.getKettleServiceBasePath() +
                   KettleServiceClientEndpoints.UNIT_PRODUCT_INVENTORY_LIVE_STATUS, environmentProperties.getChannelPartnerClientToken(),
               data, Map.class);
        }catch(Exception e){
            LOG.error("Unable to fetch unit product Inventory data for web for unit :::{}", unitId, e);
        }
        return null;
    }


    private List<InventoryInfo> getTrimmedDownUnitInventory(Integer unitId, List<Integer> productIds) {
        LOG.info("Getting Unit Inventory details for ID = {}",  unitId);
        if (masterDataCache.getUnitBasicDetail(unitId).isLiveInventoryEnabled()) {
            Map map = new HashMap();
            map.put("unitId", unitId + "");
            map.put("productIds", productIds);
            return getLiveInventoryForProducts(map);
        }
        return null;
    }

    private List<InventoryInfo> getLiveInventoryForProducts(Map productIdsMap) {
        long startTime = System.currentTimeMillis();
        String unitZone =masterDataCache.getUnitBasicDetail(Integer.parseInt(productIdsMap.get("unitId").toString())).getUnitZone();
        String endPoint = environmentProperties.getInventoryBaseUrl() + InventoryServiceEndPoints.INVENTORY_SERVICE_ENTRY_POINT
                + (Objects.nonNull(unitZone) ? unitZone.toLowerCase() : AppConstants.DEFAULT_UNIT_ZONE) +
                InventoryServiceEndPoints.INVENTORY_SERVICE_VERSION + InventoryServiceEndPoints.GET_CAFE_INVENTORY_INFO_PRODUCTS;
        List<InventoryInfo> data = new ArrayList<>();
        try {
            List<?> list = com.stpl.tech.master.core.WebServiceHelper.postRequestWithAuthInternalWithTimeout(endPoint, productIdsMap, List.class,
                    null);
            GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
            list.forEach(p -> {
                Gson gson = gSonBuilder.create();
                String str = gson.toJson(p);
                InventoryInfo cat = gson.fromJson(str, InventoryInfo.class);
                if (cat != null) {
                    data.add(cat);
                }
            });
        } catch (Exception e) {
            LOG.error("Error while creating request for inventory for unit products", e);
        }
        LOG.info("Inventory Data collected from inventory Service in {} miliseconds",
                System.currentTimeMillis() - startTime);
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public void cancelOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual, String cancellationReason, Integer approvedBy) {
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Map<String, Object> uriVariables = new HashMap<>();
        ActionRequest request = new ActionRequest();
        Order order = new Gson().fromJson(new Gson().toJson(partnerOrderDetail.getKettleOrder()), Order.class);
        request.setGeneratedOrderId(order.getGenerateOrderId());
        request.setOrderId(order.getOrderId());
        request.setOrderStatus(OrderStatus.CANCELLED_REQUESTED);
        request.setApprovedBy(approvedBy);
        if(!StringUtils.isEmpty(cancellationReason)){
            request.setReason(cancellationReason);
        }else{
            request.setReason(ChannelPartnerServiceConstants.WASTAGE_REASON);
        }
        request.setReasonId(ChannelPartnerServiceConstants.WASTAGE_REASON_ID);
        request.setUnitId(order.getUnitId());
        request.setChannelPartner(order.getChannelPartner());
        request.setOrderSource(UnitCategory.valueOf(order.getSource()));
        request.setNoTimeConstraint(true);
        request.setBookWastage(ChannelPartnerServiceConstants.COMPLETE_WASTAGE);
        RequestData data = new RequestData();
        UserSessionDetail userSessionDetail = new UserSessionDetail();
        userSessionDetail.setUserId(ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID);
        userSessionDetail.setBusinessDate(ChannelPartnerUtils.getBusinessDate());
        data.setSession(userSessionDetail);
        data.setData(new Gson().toJson(request));
        try {
            Boolean cancelled = webServiceHelper.callInternalApi(environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.CANCEL_ORDER,
                environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, Boolean.class, data, uriVariables);
            if (Boolean.TRUE.equals(cancelled)) {
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                    PartnerOrderStatus.CANCELLED, isManual, currentTime));
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCELLED);
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.CANCELLED, "Order Cancelled!", null));
            } else {
                //LOG cancellation request failed
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.CANCEL_REQUEST_FAILED,
                    "Order cancellation failed from kettle", currentTime));
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                    PartnerOrderStatus.CANCEL_REQUEST_FAILED, isManual, currentTime));
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCEL_REQUEST_FAILED);
            }
        } catch (Exception e) {
            LOG.error("Zomato order cancellation exception {}", partnerOrderDetail.getPartnerOrderId(), e);
            partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.CANCEL_REQUEST_FAILED,
                "Order cancellation failed from kettle", currentTime));
            partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                PartnerOrderStatus.CANCEL_REQUEST_FAILED, isManual, currentTime));
            partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCEL_REQUEST_FAILED);
        }
        trackService.updatePartnerOrder(partnerOrderDetail);
    }

    @Override
    public void sendMarkOutOfStockRequest(PartnerOrderDetail partnerOrderDetail, Order order, List<OrderItem> outOfStockItems
    ,SwiggyService swiggyService) {
        LOG.info("Out of stock items List on Swiggy :::::::{}", new Gson().toJson(outOfStockItems));
        try {
            List<String> outOfStockItemsForSwiggy = getOutOfStockItems(partnerOrderDetail, outOfStockItems);
             markOutOfStockItemsForSwiggy(partnerOrderDetail, outOfStockItemsForSwiggy,swiggyService);
        } catch (Exception e) {
            LOG.error("Error marking item OOS Swiggy", e);
        }

    }


    private boolean compareTime(String open, String close, Date current) {
        try {
            Date start = AppUtils.setTimeToDate(AppUtils.getCurrentTimestamp(), AppUtils.timeToDate(open), true);
            Date end = AppUtils.setTimeToDate(AppUtils.getCurrentTimestamp(), AppUtils.timeToDate(close), false);
            if (end.compareTo(start) > 0) {
                return start.compareTo(current) <= 0 && end.compareTo(current) >= 0;
            } else {
                Date end1 = AppUtils.getUpdatedTimeInDate(23, 59, 59, end);
                if (current.compareTo(start) >= 0 && current.compareTo(end1) < 0) {
                    return start.compareTo(current) <= 0 && end1.compareTo(current) >= 0;
                } else {
                    Date start1 = AppUtils.getUpdatedTimeInDate(0, 0, 0, start);
                    return start1.compareTo(current) <= 0 && end.compareTo(current) >= 0;
                }
            }
        } catch (Exception e) {
            LOG.error("Error while time comparison", e);
            return false;
        }
    }

    private boolean isAcceptableVariance(BigDecimal value, BigDecimal value2) {
        // variance should be less then 0.02
        if (value == null) {
            value = BigDecimal.ZERO;
        }
        if (value2 == null) {
            value2 = BigDecimal.ZERO;
        }
        return value.subtract(value2).abs().compareTo(BigDecimal.valueOf(0.02)) < 0;
    }

    private void getResult(Unit unit, UnitProductTaxCodeMap unitProductTaxCodeMap) {
        if (unit.getLocation() != null) {
            int stateId = unit.getLocation().getState().getId();
            Set<String> codes = new HashSet<>();
            unitProductTaxCodeMap.getProductVOMap().values().forEach(product -> {
                if (product.getTaxCode() != null && !codes.contains(product.getTaxCode())) {
                    TaxData tax = taxDataCache.getTaxData(stateId, product.getTaxCode());
                    if (tax != null && tax.getState() != null) {
                        if (unitProductTaxCodeMap.getTaxMap() == null) {
                            unitProductTaxCodeMap.setTaxMap(new HashMap<>());
                        }
                        unitProductTaxCodeMap.getTaxMap().put(tax.getTaxCode(), new TaxDataVO(tax));
                    }
                    codes.add(product.getTaxCode());
                }
            });
        }
    }

    private void publishPriceMismatchToSlack(PartnerOrderDetail partnerOrderDetail) {
        boolean priceMismatchFound = false;
        StringBuilder error = new StringBuilder("PRICE MISMATCH FOUND " + partnerOrderDetail.getPartnerName() +
            " for Order: #" + partnerOrderDetail.getPartnerOrderId());
        for (PartnerOrderError partnerOrderError : partnerOrderDetail.getOrderErrors()) {
            if (PartnerOrderErrorCode.PRICE_MISMATCH.equals(partnerOrderError.getErrorCode())) {
                if (!priceMismatchFound) {
                    priceMismatchFound = true;
                }
                error.append("\n").append(partnerOrderError.getErrorDescription());
            }
        }
        if (priceMismatchFound) {
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION, error.toString());
        }
    }

    private void publishTransactionMismatchToSlack(PartnerOrderDetail partnerOrderDetail) {
        PartnerOrderError partnerOrderError = partnerOrderDetail.getOrderErrors().get(partnerOrderDetail.getOrderErrors().size() - 1);
//        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
//            ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
//            "PAID AMOUNT MISMATCH FOUND " + partnerOrderDetail.getPartnerName() + " for Order: #" +
//                partnerOrderDetail.getPartnerOrderId() + "\n" + partnerOrderError.getErrorDescription());
    }


    private void sendInventoryDownNotification(int unitId,int customerId) {
        try {
            UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(unitId);
//            String message = "Inventory service is down for UNIT:" + unitId + " through Channel Partner"+
//                    " But order has been punched for customer id: " + customerId + " at " + AppUtils.getCurrentTimestamp() + ", please get this sorted ASAP";
            String message = ChannelPartnerUtils.getMessage("Inventory service is down","::::::::::::::::::::::Details::::::::::::::::::::::"+"\n"+"Unit:"+unitId+" through Channel Partner"+
                    " But order has been punched for customer id: " + customerId + " at " + AppUtils.getCurrentTimestamp() + ", please get this sorted ASAP");
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                "Channel Partner", SlackNotification.ORDER_INVENTORY_STATUS, message);
            if (ubd.getUnitManagerId() != null) {
                SlackNotificationService.getInstance()
                    .sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
                        !AppUtils.isProd(environmentProperties.getEnvType())
                            ? environmentProperties.getEnvType().name().toLowerCase() + "_"
                            + ubd.getUnitManagerId() + "_notify"
                            : ubd.getUnitManagerId() + "_notify",
                        message);
            }
            if (ubd.getCafeManagerId() != null) {
                SlackNotificationService.getInstance()
                    .sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
                        !AppUtils.isProd(environmentProperties.getEnvType())
                            ? environmentProperties.getEnvType().name().toLowerCase() + "_"
                            + ubd.getCafeManagerId() + "_notify"
                            : ubd.getCafeManagerId() + "_notify",
                        message);
            }
        } catch (Exception e) {
            LOG.error("Error while publishing slack for inventory down and order punched", e);
        }
    }

    private void sendInventoryDownNotificationToKnock(int unitId,int customerId) {
        try {
            UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(unitId);
            String knock_message = "Inventory service is down for UNIT:" + unitId + " through Channel Partner"+
                    " But order has been punched for customer id: " + customerId + " at " + AppUtils.getCurrentTimestamp() + ", please get this sorted ASAP";
            Map<String,String> params = new HashMap<>();
            params.put("message",knock_message);
            params.put("title", INVENTORY_DOWN);
            try {
                if (ubd.getUnitManagerId() != null) {
                    params.put("userId", ubd.getUnitManagerId() + "");
                    webServiceHelper.postWithAuthentication(environmentProperties.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + SEND_KNOCK_NOTIFICATION, environmentProperties.getKnockMasterToken(), params,null, Boolean.class);
                }
            } catch (Exception e) {
                LOG.error("Error while publishing notification on knock for inventory down and order punched ", e);
            }
            try {
                if (ubd.getCafeManagerId() != null) {
                    params.put("userId", ubd.getCafeManagerId() + "");
                    webServiceHelper.postWithAuthentication(environmentProperties.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + SEND_KNOCK_NOTIFICATION, environmentProperties.getKnockMasterToken(), params,null, Boolean.class);
                }
            }catch(Exception e){
                LOG.error("Error while publishing notification on knock for inventory down and order punched", e);
            }
        } catch (Exception e) {
            LOG.error("Error while publishing slack for inventory down and order punched", e);
        }
    }
}

