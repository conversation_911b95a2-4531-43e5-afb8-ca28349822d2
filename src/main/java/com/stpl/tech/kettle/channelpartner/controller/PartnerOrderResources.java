package com.stpl.tech.kettle.channelpartner.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderResponse;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.impl.OrderValidationServiceImpl;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPendingOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderComplaint;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerRequest;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderComplaintTrackDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderFallbackLogDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderComplaintTrack;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.PARTNER_ORDER_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PARTNER_ORDER_ROOT_CONTEXT) // 'v1/partner-order'
public class PartnerOrderResources extends ChannelPartnerAbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerOrderResources.class);

    @Autowired
    private PartnerOrderService partnerOrderService;

    @Autowired
    private SwiggyService swiggyService;
    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @Autowired
    private OrderValidationServiceImpl orderValidationService;

    @Autowired
    private PartnerOrderFallbackLogDao partnerOrderFallbackLogDao;

    @Autowired
    private PartnerOrderComplaintTrackDao partnerOrderComplaintTrackDao;

    @Autowired
    private ZomatoService zomatoService;

    @RequestMapping(method = RequestMethod.POST, value = "get", produces = MediaType.APPLICATION_JSON)
    public List<PartnerOrderDetail> getPartnerOrder(@RequestBody PartnerOrderDetail request) throws ChannelPartnerException {
        LOG.info("Request to get partner order : " + new Gson().toJson(request));
        return partnerOrderService.getPartnerOrder(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-pending-orders", produces = MediaType.APPLICATION_JSON)
    public Map<String, List<PartnerOrderDetail>> getPartnerOrder(@RequestBody PartnerPendingOrderRequest request) throws ChannelPartnerException {
        if(request.getHours() == null){
            request.setHours(2);
        }
        return partnerOrderService.getPartnerPendingOrders(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "call-swiggy-support", produces = MediaType.APPLICATION_JSON)
    public boolean callSwiggyPartnerSupport(@RequestParam String orderId , HttpServletRequest request) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to call swiggy partner support for order id: " + orderId);
        return partnerOrderService.callSwiggyPartnerSupport(orderId , getLoggedInUser(request).toString());
        //Call Swiggy Support
    }

    @RequestMapping(method = RequestMethod.POST, value = "mark-resolved", produces = MediaType.APPLICATION_JSON)
    public boolean markOrderIssuesResolved(@RequestParam String orderId,HttpServletRequest request) throws ChannelPartnerException {
        LOG.info("Request to mark order resolved for order id: " + orderId);
        return partnerOrderService.markOrderResolved(orderId,getLoggedInUser(request).toString());
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-kettle-order", produces = MediaType.APPLICATION_JSON)
    public boolean addKettleOrderForCheckedOrder(@RequestParam String orderId , @RequestParam String kettleOrderId ,@RequestParam Integer unitId,HttpServletRequest request ) throws ChannelPartnerException {
        LOG.info("Request to Add Kettle Order :: {} To Partner Order :: {}  " ,kettleOrderId, orderId);
        return partnerOrderService.addKettleOrder(kettleOrderId,orderId,unitId,getLoggedInUser(request).toString());
    }

    @RequestMapping(method = RequestMethod.POST, value = "manual-process", produces = MediaType.APPLICATION_JSON)
    public boolean manualProcess(@RequestParam String orderId , HttpServletRequest request) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to manual process order id: " + orderId);
        return partnerOrderService.manualProcess(orderId,getLoggedInUser(request).toString());
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-magicpin-order-items", produces = MediaType.APPLICATION_JSON)
    public List<PartnerItemData> getMagicPinitems(@RequestParam String orderId) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to get Items for Magicpin order id: " + orderId);
        return partnerOrderService.getMagicPinOrderItems(orderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "mark-cancelled", produces = MediaType.APPLICATION_JSON)
    public boolean markCancelled(@RequestParam String kettleOrderId,HttpServletRequest request) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to mark cancel order id: " + kettleOrderId);
        return partnerOrderService.markCancelled(kettleOrderId,getLoggedInUser(request).toString());
    }

    @RequestMapping(method = RequestMethod.POST, value = "force-process", produces = MediaType.APPLICATION_JSON)
    public boolean skipInventoryCheck(@RequestParam String orderId , HttpServletRequest request) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to place order without inventory order id: " + orderId);
        return partnerOrderService.manualProcessWithSkipInventory(orderId,getLoggedInUser(request).toString());
    }

    @RequestMapping(method = RequestMethod.POST, value = "swiggy-rider-time", produces = MediaType.APPLICATION_JSON)
    public Integer getSwiggyRiderTimeOfArrival(@RequestBody String orderId) throws ChannelPartnerException {
        LOG.info("Request to get Swiggy rider time of arrival: " + orderId);
        return partnerOrderService.getSwiggyRiderTimeOfArrival(orderId);
    }

    @RequestMapping(method = RequestMethod.GET,value = "update-cafe-timing",produces = MediaType.APPLICATION_JSON)
    public void updateCafeBuisnessTiming(){

    }

    @RequestMapping(method = RequestMethod.POST, value = "get-product-detail-by-name-orderId", produces = MediaType.APPLICATION_JSON)
    public PartnerOrderDetail getProductDetailByNameAndOrderId(@RequestBody PartnerRequest partnerRequest) {
        LOG.info("request to get product detail by name and order id : " + new Gson().toJson(partnerRequest));
        return partnerOrderDao.getProductDetailByOrderIdAndPartnerOrderStatus(partnerRequest.getPartnerOrderId()).get(0);
    }

    @RequestMapping(method = RequestMethod.POST, value = "rejection-product-detail", produces = MediaType.APPLICATION_JSON)
    public PartnerOrderDetail getRejectionProductDetail(@RequestBody PartnerRequest partnerRequest) {
        List<String> partnerOrderStatuses = new ArrayList<>(Arrays.asList("CANCELLED", "CANCEL_REQUESTED"));
        return partnerOrderDao.getProductDetailByNameAndOrderIdAndPartnerOrderStatus(partnerRequest.getPartnerName(),
                partnerRequest.getPartnerOrderId(), partnerOrderStatuses).get(0);
    }

    @RequestMapping(method = RequestMethod.POST, value = "stock-out-notification", produces = MediaType.APPLICATION_JSON)
    public boolean stockOutNotification(@RequestBody String orderId) throws ChannelPartnerException {
        Optional<PartnerOrderDetail> partnerOrderDetail = partnerOrderDao.findById(String.valueOf(orderId));
        List<OrderItem> outOfStockItems = partnerOrderDetail.get().getStockOutProductIds().stream().map(itemId -> {OrderItem oi = new OrderItem();
        oi.setProductId(itemId);
        return oi;
        }).toList();
        boolean customerCallEventExists = partnerOrderFallbackLogDao.checkSwiggyCxSupport(partnerOrderDetail.get().getPartnerOrderId());
        if(!customerCallEventExists){
            throw new ChannelPartnerException("First contact swiggy support and customer");
        }
        orderValidationService.sendMarkOutOfStockRequest(partnerOrderDetail.orElse(null), new Order(), outOfStockItems, this.swiggyService);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "fallback-processed-by", produces = MediaType.APPLICATION_JSON)
    public boolean fallbackProcessedBy(@RequestParam String orderId, HttpServletRequest request) throws ChannelPartnerException {
        LOG.info("Request to update fallback processed by for order id: " + orderId + ", fallbackProcessedBy: " + getLoggedInUser(request));
        return partnerOrderService.fallbackProcessedBy(orderId, getLoggedInUser(request).toString());
    }

    @RequestMapping(method = RequestMethod.POST, value = "partner-order-response", produces = MediaType.APPLICATION_JSON)
    public boolean partnerOrderResponse(@RequestBody PartnerOrderResponse response, HttpServletRequest request) {
        return partnerOrderService.partnerResponse(response,getLoggedInUser(request).toString());
    }


    @Scheduled(cron = "0 04 19 * * *", zone = "GMT+05:30")
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void partnerOrderComplaintTrack() {
        Date fromDate = AppUtils.getDateAfterDays(ChannelPartnerUtils.getCurrentDate(), -1);
        Date toDate = ChannelPartnerUtils.getCurrentTimestamp();
            List<PartnerOrderComplaintTrack> pendingComplaints = partnerOrderComplaintTrackDao.findByStatusAndDateRange(AppConstants.PENDING_REQUEST, fromDate, toDate);
            LOG.info("Pending Complaints-zomato: " + pendingComplaints.size());
        for (PartnerOrderComplaintTrack complaint : pendingComplaints) {
            try {
                ZomatoOrderComplaint parsedComplaint =
                        new Gson().fromJson(complaint.getJsonData(), ZomatoOrderComplaint.class);

                boolean success = zomatoService.routeComplaintRequestToN8N(parsedComplaint);

                complaint.setStatus(success ? AppConstants.SUCCESS : AppConstants.PENDING_REQUEST);
                partnerOrderComplaintTrackDao.update(complaint);
            } catch (Exception ex) {
                LOG.error("Retry failed for order ID {}: {}", complaint.getPartnerOrderId(), ex.getMessage());
            }
        }
    }

    @GetMapping(value = "order-complaints", produces = MediaType.APPLICATION_JSON)
    public ResponseEntity<List<PartnerOrderComplaintTrack>> getComplaints(
            @RequestParam(value = "status", defaultValue = AppConstants.PENDING_REQUEST) String status,
            @RequestParam(value = "days", defaultValue = "1") int days) {
        try {
            Date fromDate = AppUtils.getDateAfterDays(ChannelPartnerUtils.getCurrentDate(), -days);
            Date toDate = ChannelPartnerUtils.getCurrentTimestamp();
            List<PartnerOrderComplaintTrack> pendingComplaints =
                    partnerOrderComplaintTrackDao.findByStatusAndDateRange(status, fromDate, toDate);
            for (PartnerOrderComplaintTrack complaint : pendingComplaints) {
                    ZomatoOrderComplaint parsedComplaint =
                            new Gson().fromJson(complaint.getJsonData(), ZomatoOrderComplaint.class);

                    boolean success = zomatoService.routeComplaintRequestToN8N(parsedComplaint);

                    complaint.setStatus(success ? AppConstants.SUCCESS : AppConstants.PENDING_REQUEST);
                    partnerOrderComplaintTrackDao.update(complaint);
            }
            return new ResponseEntity<>(pendingComplaints, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("Error fetching complaints: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


}
